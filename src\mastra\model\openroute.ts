import { createOpenRouter } from "@openrouter/ai-sdk-provider";
import { generateText } from "ai";

const kilocodeToken =
  //   process.env.KILO_CODE_TOKEN ||
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************.cdqqfeO8_LXgNO97APfrj61uDgXEnYQ0XFeVNa0Wvts";
export const openrouter = createOpenRouter({
  baseURL: "https://kilocode.ai/api/openrouter",
  apiKey: kilocodeToken,
  headers: {
    Authorization: `Bearer ${kilocodeToken}`,
    "Content-Type": "application/json"
  },
  compatibility: "compatible"
});
