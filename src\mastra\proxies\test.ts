import { 
  getAvailableProxy, 
  recordProxyError, 
  recordProxySuccess, 
  getProxyStats, 
  performHealthCheck,
  initializeProxyPool 
} from './index';

async function testProxyPool() {
  console.log('🧪 Testing Proxy Pool...\n');

  try {
    // Test 1: Initialize proxy pool
    console.log('1. Initializing proxy pool...');
    await initializeProxyPool({
      refreshIntervalMs: 60 * 1000, // 1 minute for testing
      maxErrorCount: 2,
      healthCheckTimeoutMs: 2000
    });
    console.log('✅ Proxy pool initialized\n');

    // Test 2: Get initial stats
    console.log('2. Getting initial stats...');
    let stats = await getProxyStats();
    if (!stats) {
      console.log('⚠️  No stats available yet');
      return;
    }
    console.log(`📊 Stats: ${stats.healthy}/${stats.total} healthy proxies`);
    console.log(`📈 Total usage: ${stats.totalUsage}, Total errors: ${stats.totalErrors}\n`);

    if (stats.total === 0) {
      console.log('⚠️  No proxies available. Check your environment variables:');
      console.log('   - SCRAPINGDOG_PROXY_URL');
      console.log('   - WEBSHARE_PROXIES_URL');
      return;
    }

    // Test 3: Get proxies and simulate usage
    console.log('3. Testing proxy rotation...');
    const usedProxies = new Set<string>();
    
    for (let i = 0; i < Math.min(5, stats.total); i++) {
      const proxy = await getAvailableProxy();
      if (proxy) {
        const maskedProxy = proxy.replace(/\/\/.*@/, '//***@');
        console.log(`🔄 Got proxy ${i + 1}: ${maskedProxy}`);
        usedProxies.add(proxy);
        
        // Simulate successful use
        recordProxySuccess(proxy);
      } else {
        console.log('❌ No proxy available');
        break;
      }
    }
    console.log(`✅ Used ${usedProxies.size} different proxies\n`);

    // Test 4: Simulate errors
    console.log('4. Testing error handling...');
    const testProxy = await getAvailableProxy();
    if (testProxy) {
      const maskedProxy = testProxy.replace(/\/\/.*@/, '//***@');
      console.log(`🔥 Simulating errors for: ${maskedProxy}`);
      
      // Record multiple errors to test unhealthy marking
      for (let i = 0; i < 3; i++) {
        recordProxyError(testProxy);
        console.log(`   Error ${i + 1} recorded`);
      }
    }
    console.log('✅ Error simulation completed\n');

    // Test 5: Check stats after errors
    console.log('5. Checking stats after errors...');
    stats = await getProxyStats();
    if (!stats) {
      console.log('⚠️  No stats available');
      return;
    }
    console.log(`📊 Updated stats: ${stats.healthy}/${stats.total} healthy proxies`);
    console.log(`📈 Total usage: ${stats.totalUsage}, Total errors: ${stats.totalErrors}`);

    // Show detailed proxy info
    console.log('\n📋 Detailed proxy status:');
    stats.proxies.slice(0, 3).forEach((proxy, index) => {
      console.log(`   ${index + 1}. ${proxy.isHealthy ? '✅' : '❌'} Usage: ${proxy.usageCount}, Errors: ${proxy.errorCount}`);
    });
    console.log('');

    // Test 6: Health check (conceptual)
    console.log('6. Running health check...');
    console.log('⚠️  Note: Health check is conceptual in this implementation');
    console.log('   In production, you would implement actual HTTP requests through proxies');
    await performHealthCheck();
    console.log('✅ Health check completed\n');

    // Test 7: Final stats
    console.log('7. Final statistics...');
    const finalStats = await getProxyStats();
    if (finalStats) {
      console.log(`📊 Final stats: ${finalStats.healthy}/${finalStats.total} healthy proxies`);
      console.log(`📈 Total usage: ${finalStats.totalUsage}, Total errors: ${finalStats.totalErrors}`);
    }

    console.log('\n🎉 All tests completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Helper function to test environment setup
function checkEnvironment() {
  console.log('🔍 Checking environment variables...');
  
  const requiredVars = ['SCRAPINGDOG_PROXY_URL', 'WEBSHARE_PROXIES_URL'];
  const missing = requiredVars.filter(varName => !process.env[varName]);
  
  if (missing.length > 0) {
    console.log('❌ Missing environment variables:');
    missing.forEach(varName => console.log(`   - ${varName}`));
    console.log('\nPlease set these variables before running the proxy pool.\n');
    return false;
  }
  
  console.log('✅ All required environment variables are set\n');
  return true;
}

// Run tests if this file is executed directly
if (require.main === module) {
  if (checkEnvironment()) {
    testProxyPool().catch(console.error);
  }
}

export { testProxyPool, checkEnvironment };
