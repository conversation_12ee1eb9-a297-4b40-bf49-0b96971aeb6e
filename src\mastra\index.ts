import { <PERSON><PERSON> } from "@mastra/core/mastra";
import { <PERSON><PERSON>Logger } from "@mastra/loggers";
import { LibSQLStore } from "@mastra/libsql";
import { weatherWorkflow } from "./workflows/weather-workflow";
import { weatherAgent } from "./agents/weather-agent";
import { blackhatWorldAgent } from "./agents/blackhatworld-agent";

export const mastra = new Mastra({
  workflows: { weatherWorkflow },
  agents: {
    weatherAgent,
    blackhatWorldAgent
  },
  storage: new LibSQLStore({
    // stores telemetry, evals, ... into memory storage, if it needs to persist, change to file:../mastra.db
    url: ":memory:"
  }),
  logger: new PinoLogger({
    name: "<PERSON><PERSON>",
    level: "info"
  })
});
