{"name": "next-agent", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "vitest run", "test:watch": "vitest", "test:agent": "vitest run src/mastra/agents/blackhatworld-agent.test.ts", "test:integration": "vitest run src/mastra/agents/blackhatworld-agent.integration.test.ts", "test:deepwiki": "vitest run src/mastra/agents/deepwiki-agent-improved.test.ts", "test:github": "vitest run src/mastra/agents/github-agent.test.ts", "test:coverage": "vitest run --coverage", "dev": "<PERSON>ra dev", "build": "mastra build", "start": "mastra start"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "engines": {"node": ">=20.9.0"}, "dependencies": {"@ai-sdk/openai": "^1.3.23", "@mastra/core": "^0.11.1", "@mastra/libsql": "^0.11.2", "@mastra/loggers": "^0.10.4", "@mastra/mcp": "^0.10.7", "@mastra/memory": "^0.11.5", "@mendable/firecrawl-js": "^1.29.2", "@openrouter/ai-sdk-provider": "^0.7.3", "ai": "^4.3.19", "cheerio": "^1.1.2", "form-data": "^4.0.4", "p-retry": "^6.2.1", "undici": "^7.12.0", "zod": "3"}, "devDependencies": {"@mastra/evals": "^0.10.7", "@types/node": "^24.1.0", "@vitest/coverage-v8": "^2.0.0", "mastra": "^0.10.15", "prettier": "^3.6.2", "typescript": "^5.8.3", "vitest": "^2.0.0"}}