import { ProxyPool } from './pool';
import type { ProxyPoolConfig, ProxyStats } from './types';

class ProxyManager {
  private pool: ProxyPool | null = null;
  private isInitializing = false;

  async getPool(): Promise<ProxyPool> {
    if (this.pool) {
      return this.pool;
    }

    if (this.isInitializing) {
      // Wait for initialization to complete
      while (this.isInitializing) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      return this.pool!;
    }

    this.isInitializing = true;
    try {
      this.pool = new ProxyPool();
      await this.pool.initialize();
      return this.pool;
    } finally {
      this.isInitializing = false;
    }
  }

  async initializeWithConfig(config: Partial<ProxyPoolConfig>): Promise<void> {
    if (this.pool) {
      this.pool.destroy();
    }
    
    this.pool = new ProxyPool(config);
    await this.pool.initialize();
  }

  async getAvailableProxy(): Promise<string | null> {
    const pool = await this.getPool();
    return pool.getNextProxy();
  }

  async recordError(proxyUrl: string): Promise<void> {
    if (this.pool) {
      this.pool.recordError(proxyUrl);
    }
  }

  async recordSuccess(proxyUrl: string): Promise<void> {
    if (this.pool) {
      this.pool.recordSuccess(proxyUrl);
    }
  }

  async getStats(): Promise<ProxyStats | null> {
    if (this.pool) {
      return this.pool.getStats();
    }
    return null;
  }

  async performHealthCheck(): Promise<void> {
    if (this.pool) {
      await this.pool.healthCheck();
    }
  }

  destroy(): void {
    if (this.pool) {
      this.pool.destroy();
      this.pool = null;
    }
  }
}

// Singleton instance
export const proxyManager = new ProxyManager();
