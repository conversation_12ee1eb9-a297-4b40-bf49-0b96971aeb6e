import FireCrawlApp from '@mendable/firecrawl-js';
import * as cheerio from 'cheerio';

const app = new FireCrawlApp({ apiKey: process.env.FIRECRAWL_API_KEY || "fc-dd20fa1c0ec4435a871e535d538b4b2f" });

// 线程信息接口
interface ThreadInfo {
  url: string;
  title: string;
  replies: number;
  views: number;
  author: string;
  lastPost: string;
}

// 文章内容接口
interface ArticleContent {
  url: string;
  title: string;
  content: string;
  author: string;
  postDate: string;
  replies: number;
  views: number;
}

/**
 * 从论坛页面解析有回复的线程链接
 */
export const resolveThreads = async (forumUrl: string, minReplies: number = 5): Promise<ThreadInfo[]> => {
  try {
    const scrapeResult = await app.scrapeUrl(forumUrl, {
      formats: ["rawHtml"],
      onlyMainContent: false,
      parsePDF: false,
      maxAge: 14400000
    });

    if (!scrapeResult.success || !scrapeResult.rawHtml) {
      throw new Error('Failed to scrape forum page');
    }

    const $ = cheerio.load(scrapeResult.rawHtml);
    const threads: ThreadInfo[] = [];

    $('.structItemContainer-group .structItem:not(:has(.sticky-thread--hightlighted))').each((_, element) => {
      const $item = $(element);
      
      const titleElement = $item.find('a[data-preview-url]').first();
      const title = titleElement.text().trim();
      const url = titleElement.attr('href');
      
      if (!title || !url) return;

      const repliesText = $item.find('.structItem-cell--meta dl:first-child dd').text().trim();
      const viewsText = $item.find('.structItem-cell--meta dl:nth-child(2) dd').text().trim();
      const replies = parseInt(repliesText.replace(/,/g, '')) || 0;
      const views = parseInt(viewsText.replace(/,/g, '')) || 0;

      // 只包含回复数大于指定值的线程
      if (replies >= minReplies) {
        const author = $item.find('.username').first().text().trim();
        const lastPost = $item.find('.structItem-cell--latest time').attr('title') || 
                        $item.find('.structItem-cell--latest time').text().trim();

        threads.push({
          url: url.startsWith('http') ? url : `https://www.blackhatworld.com${url}`,
          title,
          replies,
          views,
          author,
          lastPost
        });
      }
    });

    console.log(`Found ${threads.length} threads with ${minReplies}+ replies`);
    return threads;

  } catch (error) {
    console.error('Error resolving threads:', error);
    return [];
  }
};

/**
 * 抓取单个文章内容
 */
export const scrapeArticle = async (url: string): Promise<ArticleContent | null> => {
  try {
    const scrapeResult = await app.scrapeUrl(url, {
      formats: ["markdown"],
      onlyMainContent: true,
      includeTags: ["[data-message-selector=\".js-post\"]"],
      parsePDF: false,
      maxAge: 14400000
    });

    if (!scrapeResult.success || !scrapeResult.markdown) {
      console.error('Failed to scrape article:', url);
      return null;
    }

    // 从URL中提取基本信息（如果可能）
    const urlParts = url.split('/');
    const titleFromUrl = urlParts[urlParts.length - 2]?.replace(/[-_]/g, ' ') || 'Unknown Title';

    return {
      url,
      title: titleFromUrl,
      content: scrapeResult.markdown,
      author: 'Unknown', // 需要从HTML中解析
      postDate: new Date().toISOString().split('T')[0],
      replies: 0,
      views: 0
    };

  } catch (error) {
    console.error('Error scraping article:', error);
    return null;
  }
};

/**
 * 批量抓取多个论坛页面的线程
 */
export const scrapeMultipleForums = async (
  forumUrls: string[], 
  minReplies: number = 5
): Promise<ThreadInfo[]> => {
  const allThreads: ThreadInfo[] = [];

  for (const forumUrl of forumUrls) {
    console.log(`Scraping forum: ${forumUrl}`);
    const threads = await resolveThreads(forumUrl, minReplies);
    allThreads.push(...threads);
    console.log(`Found ${threads.length} valid threads from this forum`);
  }

  console.log(`Total threads found: ${allThreads.length}`);
  return allThreads;
};

/**
 * 预定义的热门论坛URL
 */
export const POPULAR_FORUMS = [
  "https://www.blackhatworld.com/forums/making-money.12/?last_days=7&order=post_date&direction=desc",
  "https://www.blackhatworld.com/forums/black-hat-seo.1/?last_days=7&order=post_date&direction=desc",
  "https://www.blackhatworld.com/forums/white-hat-seo.95/?last_days=7&order=post_date&direction=desc",
  "https://www.blackhatworld.com/forums/social-media.270/?last_days=7&order=post_date&direction=desc"
];

/**
 * 快速获取热门线程
 */
export const getPopularThreads = async (minReplies: number = 10): Promise<ThreadInfo[]> => {
  return await scrapeMultipleForums(POPULAR_FORUMS, minReplies);
};
