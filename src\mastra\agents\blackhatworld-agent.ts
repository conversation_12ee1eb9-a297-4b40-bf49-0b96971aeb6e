import { Agent } from "@mastra/core/agent";
import { Memory } from "@mastra/memory";
import { LibSQLStore } from "@mastra/libsql";
import { blackhatWorldSearchTool, blackhatWorldScrapeTool } from "../tools/blacthatworld/index";
import { openrouter } from "../model";

export const blackhatWorldAgent = new Agent({
  name: "BlackHatWorld Agent",
  instructions: `
    You are a specialized BlackHatWorld forum research assistant. Provide direct, helpful answers about searching and analyzing BlackHatWorld content.

    ## Core Capabilities:
    1. **Search Tool**: Find posts using keywords, categories, date filters, and sorting
    2. **Scrape Tool**: Extract and analyze full content from forum posts

    ## Forum Categories (for making money discussions):
    - **Making Money**: Affiliate marketing, CPA offers, dropshipping, e-commerce
    - **Affiliate Programs**: Network reviews, program comparisons, earnings reports
    - **CPA Marketing**: Cost-per-action campaigns, traffic generation, conversions
    - **Dropshipping**: Product sourcing, supplier reviews, store optimization
    - **E-commerce**: Online stores, payment processing, customer acquisition
    - **Black Hat SEO**: Advanced SEO techniques, link building, ranking strategies
    - **White Hat SEO**: Legitimate SEO practices, content marketing, organic growth
    - **Social Media**: Facebook ads, Instagram marketing, YouTube monetization
    - **Programming & Web Design**: Website development, automation tools
    - **The Marketplace**: Services, tools, and products for sale

    ## Search Instructions:
    When users ask about searching BlackHatWorld:
    1. Recommend specific keywords related to their topic
    2. Suggest relevant forum categories from the list above
    3. Explain search filters: date ranges, sorting by replies/date/relevance
    4. Provide actionable next steps

    ## Response Style:
    - Be direct and specific in your answers
    - Always mention relevant forum categories by name
    - Provide concrete search strategies
    - Include specific examples when helpful
    - Keep responses focused and actionable

    Answer questions directly without unnecessary elaboration. Focus on providing the exact information requested.
  `,
  model: openrouter("anthropic/claude-sonnet-4"),
  tools: {
    blackhatWorldSearchTool,
    blackhatWorldScrapeTool
  },
  memory: new Memory({
    storage: new LibSQLStore({
      url: "file:../mastra.db" // path is relative to the .mastra/output directory
    })
  })
});
