#!/usr/bin/env node

/**
 * Test script for BlackHatWorld tools
 * Run with: npx tsx test-blackhatworld.ts
 */

import {
  blackhatWorldSearchTool,
  blackhatWorldScrapeTool,
  blackhatWorldDiscoverTool
} from "./src/mastra/tools/blacthatworld/index";

// Enable mock data for testing
process.env.USE_MOCK_DATA = "true";

// Create a mock runtime context
const mockRuntimeContext = {
  registry: new Map(),
  set: () => {},
  get: () => undefined,
  has: () => false,
  delete: () => false,
  clear: () => {},
  size: 0,
  keys: () => [][Symbol.iterator](),
  values: () => [][Symbol.iterator](),
  entries: () => [][Symbol.iterator](),
  forEach: () => {},
  [Symbol.iterator]: () => [][Symbol.iterator]()
};

async function testSearchTool() {
  console.log("🔍 Testing BlackHatWorld Search Tool...\n");

  try {
    const result = await blackhatWorldSearchTool.execute({
      context: {
        keywords: "content farm",
        order: "relevance",
        pages: 3
      },
      runtimeContext: mockRuntimeContext as any
    });

    console.log("✅ Search completed successfully!");
    console.log("📊 Results:", result);
    // console.log(`   - Search URL: ${result.searchUrl}`);
    // console.log(`   - Total Results: ${result.totalResults}`);
    // console.log(`   - Results Found: ${result.results?.length || 0}`);
    // console.log(`   - Current Page: ${result.currentPage}`);
    // console.log(`   - Has Next Page: ${result.hasNextPage}`);

    // if (result.results && result.results.length > 0) {
    //   console.log("\n📝 Sample Results:");
    //   result.results.slice(0, 2).forEach((item, index) => {
    //     console.log(`   ${index + 1}. ${item.title}`);
    //     console.log(`      Author: ${item.author} | Replies: ${item.replies} | Views: ${item.views}`);
    //     console.log(`      Forum: ${item.forum}`);
    //     console.log(`      URL: ${item.url}`);
    //     if (item.snippet) {
    //       console.log(`      Snippet: ${item.snippet.substring(0, 80)}...`);
    //     }
    //     console.log("");
    //   });
    // }
  } catch (error) {
    console.error("❌ Search tool test failed:", error.message);
  }
}

async function testScrapeToolMock() {
  console.log("📄 Testing BlackHatWorld Scrape Tool (Mock Mode)...\n");

  try {
    const result = await blackhatWorldScrapeTool.execute({
      context: {
        url: "https://www.blackhatworld.com/seo/monetizing-mobile-phone-farms-in-2025-affiliate-traffic-account-farming-or-proxy-rentals.1707940/"
      }
    });

    console.log("✅ Scrape completed!");
    console.log("📊 Results:");
    console.log(`   - Success: ${result.success}`);
    console.log(`   - Title: ${result.title}`);
    console.log(`   - Author: ${result.author}`);
    console.log(`   - Content Length: ${result.content.length} characters`);
    console.log(`   - Post Date: ${result.postDate}`);
    console.log(`   - Replies: ${result.replies}`);
    console.log(`   - Views: ${result.views}`);

    if (result.content) {
      console.log(`\n📝 Content Preview:`);
      console.log(`   ${result.content.substring(0, 150)}...`);
    }
  } catch (error) {
    console.error("❌ Scrape tool test failed:", error.message);
  }
}

async function testDiscoverToolMock() {
  console.log("🔎 Testing BlackHatWorld Discover Tool (Mock Mode)...\n");

  try {
    // Note: This will use mock data in development mode
    const result = await blackhatWorldDiscoverTool.execute({
      context: {
        minReplies: 5,
        usePopularForums: true
      }
    });

    console.log("✅ Discovery completed!");
    console.log("📊 Results:");
    console.log(`   - Total Found: ${result.totalFound}`);
    console.log(`   - Forums Scraped: ${result.forumsScraped.length}`);

    if (result.threads.length > 0) {
      console.log("\n📝 Sample Threads:");
      result.threads.slice(0, 3).forEach((thread, index) => {
        console.log(`   ${index + 1}. ${thread.title}`);
        console.log(`      Author: ${thread.author} | Replies: ${thread.replies} | Views: ${thread.views}`);
        console.log(`      Last Post: ${thread.lastPost}`);
        console.log(`      URL: ${thread.url}`);
        console.log("");
      });
    }
  } catch (error) {
    console.error("❌ Discover tool test failed:", error.message);
  }
}

async function testAdvancedSearch() {
  console.log("🎯 Testing Advanced Search Features...\n");

  try {
    const result = await blackhatWorldSearchTool.execute({
      context: {
        keywords: "affiliate marketing",
        nodeNames: ["Making Money", "Affiliate Programs"],
        newerThan: "2024-01-01",
        order: "replies"
      }
    });

    console.log("✅ Advanced search completed!");
    console.log("📊 Results:");
    console.log(`   - Search URL: ${result.searchUrl}`);
    console.log(`   - Results: ${result.results?.length || 0}`);

    // Test URL-only mode
    const urlOnlyResult = await blackhatWorldSearchTool.execute({
      context: {
        keywords: "SEO tools",
        parseResults: false
      }
    });

    console.log(`   - URL-only mode: ${urlOnlyResult.searchUrl ? "✅" : "❌"}`);
    console.log(`   - Available nodes provided: ${urlOnlyResult.availableNodes ? "✅" : "❌"}`);
  } catch (error) {
    console.error("❌ Advanced search test failed:", error.message);
  }
}

async function runAllTests() {
  console.log("🚀 BlackHatWorld Tools Test Suite\n");
  console.log("=".repeat(50));

  // await testSearchTool();
  // console.log("\n" + "=".repeat(50));

  await testScrapeToolMock();
  console.log("\n" + "=".repeat(50));
  return;

  await testDiscoverToolMock();
  console.log("\n" + "=".repeat(50));

  await testAdvancedSearch();
  console.log("\n" + "=".repeat(50));

  console.log("\n✨ All tests completed!");
  console.log("\n💡 Tips:");
  console.log("   - Set USE_MOCK_DATA=false for real API calls");
  console.log("   - Check the README.md for detailed usage examples");
  console.log("   - Use the BlackHatWorld Agent for intelligent assistance");
}

runAllTests().catch(console.error);
