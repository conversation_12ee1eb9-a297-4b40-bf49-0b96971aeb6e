/**
 * GitHub Search Response Parser
 * Formats and processes GitHub API responses
 */

import { GitHubSearchResponse, GitHubRepository, GitHubCodeResult } from "./client.js";

export interface ParsedRepository {
  id: number;
  name: string;
  fullName: string;
  description: string | null;
  url: string;
  cloneUrl: string;
  language: string | null;
  stars: number;
  forks: number;
  size: number;
  createdAt: string;
  updatedAt: string;
  pushedAt: string;
  topics: string[];
  license: string | null;
  owner: {
    login: string;
    type: string;
    avatarUrl: string;
  };
}

export interface ParsedCodeResult {
  fileName: string;
  filePath: string;
  url: string;
  htmlUrl: string;
  repository: {
    id: number;
    name: string;
    fullName: string;
    url: string;
    description: string | null;
    language: string | null;
    stars: number;
    forks: number;
  };
  score: number;
}

export interface ParsedGitHubResponse {
  totalCount: number;
  incompleteResults: boolean;
  searchType: "repositories" | "code";
  repositories?: ParsedRepository[];
  codeResults?: ParsedCodeResult[];
  summary: string;
  markdown: string;
}

export class GitHubParser {
  /**
   * Parse GitHub search response
   */
  parseResponse(response: GitHubSearchResponse, searchType: "repositories" | "code"): ParsedGitHubResponse {
    const parsed: ParsedGitHubResponse = {
      totalCount: response.total_count,
      incompleteResults: response.incomplete_results,
      searchType,
      summary: "",
      markdown: ""
    };

    if (searchType === "repositories") {
      parsed.repositories = this.parseRepositories(response.items as GitHubRepository[]);
      parsed.summary = this.generateRepositorySummary(parsed);
      parsed.markdown = this.formatRepositoriesAsMarkdown(parsed.repositories, parsed.totalCount);
    } else {
      parsed.codeResults = this.parseCodeResults(response.items as GitHubCodeResult[]);
      parsed.summary = this.generateCodeSummary(parsed);
      parsed.markdown = this.formatCodeResultsAsMarkdown(parsed.codeResults, parsed.totalCount);
    }

    return parsed;
  }

  /**
   * Parse repository items
   */
  private parseRepositories(items: GitHubRepository[]): ParsedRepository[] {
    return items.map(repo => ({
      id: repo.id,
      name: repo.name,
      fullName: repo.full_name,
      description: repo.description,
      url: repo.html_url,
      cloneUrl: repo.clone_url,
      language: repo.language,
      stars: repo.stargazers_count,
      forks: repo.forks_count,
      size: repo.size,
      createdAt: repo.created_at,
      updatedAt: repo.updated_at,
      pushedAt: repo.pushed_at,
      topics: repo.topics || [],
      license: repo.license,
      owner: {
        login: repo.owner.login,
        type: repo.owner.type,
        avatarUrl: repo.owner.avatar_url
      }
    }));
  }

  /**
   * Parse code result items
   */
  private parseCodeResults(items: GitHubCodeResult[]): ParsedCodeResult[] {
    return items.map(item => ({
      fileName: item.name,
      filePath: item.path,
      url: item.url,
      htmlUrl: item.html_url,
      repository: {
        id: item.repository.id,
        name: item.repository.name,
        fullName: item.repository.full_name,
        url: item.repository.html_url,
        description: item.repository.description,
        language: item.repository.language,
        stars: item.repository.stargazers_count,
        forks: item.repository.forks_count
      },
      score: item.score
    }));
  }

  /**
   * Generate summary for repository search
   */
  private generateRepositorySummary(parsed: ParsedGitHubResponse): string {
    const repos = parsed.repositories || [];
    const languages = [...new Set(repos.map(r => r.language).filter(Boolean))];
    const totalStars = repos.reduce((sum, r) => sum + r.stars, 0);
    const avgStars = repos.length > 0 ? Math.round(totalStars / repos.length) : 0;

    return `Found ${parsed.totalCount} repositories. Top ${repos.length} results include projects in ${languages.slice(0, 3).join(", ")} with an average of ${avgStars} stars.`;
  }

  /**
   * Generate summary for code search
   */
  private generateCodeSummary(parsed: ParsedGitHubResponse): string {
    const codes = parsed.codeResults || [];
    const repos = [...new Set(codes.map(c => c.repository.fullName))];
    const languages = [...new Set(codes.map(c => c.repository.language).filter(Boolean))];

    return `Found ${parsed.totalCount} code matches across ${repos.length} repositories. Results span ${languages.slice(0, 3).join(", ")} projects.`;
  }

  /**
   * Format repositories as markdown
   */
  private formatRepositoriesAsMarkdown(repositories: ParsedRepository[], totalCount: number): string {
    const lines = [
      `# GitHub Repository Search Results`,
      ``,
      `**Total Results:** ${totalCount}`,
      `**Showing:** ${repositories.length} repositories`,
      ``
    ];

    repositories.forEach((repo, index) => {
      lines.push(`## ${index + 1}. [${repo.fullName}](${repo.url})`);
      lines.push(``);
      
      if (repo.description) {
        lines.push(`**Description:** ${repo.description}`);
        lines.push(``);
      }

      lines.push(`**Stats:** ⭐ ${repo.stars} | 🍴 ${repo.forks} | 📝 ${repo.language || 'N/A'}`);
      
      if (repo.topics.length > 0) {
        lines.push(`**Topics:** ${repo.topics.map(t => `\`${t}\``).join(', ')}`);
      }
      
      lines.push(`**Owner:** [@${repo.owner.login}](https://github.com/${repo.owner.login}) (${repo.owner.type})`);
      lines.push(`**Updated:** ${new Date(repo.updatedAt).toLocaleDateString()}`);
      lines.push(``);
      lines.push(`---`);
      lines.push(``);
    });

    return lines.join('\n');
  }

  /**
   * Format code results as markdown
   */
  private formatCodeResultsAsMarkdown(codeResults: ParsedCodeResult[], totalCount: number): string {
    const lines = [
      `# GitHub Code Search Results`,
      ``,
      `**Total Results:** ${totalCount}`,
      `**Showing:** ${codeResults.length} code files`,
      ``
    ];

    codeResults.forEach((code, index) => {
      lines.push(`## ${index + 1}. [${code.fileName}](${code.htmlUrl})`);
      lines.push(``);
      lines.push(`**File Path:** \`${code.filePath}\``);
      lines.push(`**Repository:** [${code.repository.fullName}](${code.repository.url})`);
      
      if (code.repository.description) {
        lines.push(`**Repo Description:** ${code.repository.description}`);
      }
      
      lines.push(`**Repo Stats:** ⭐ ${code.repository.stars} | 🍴 ${code.repository.forks} | 📝 ${code.repository.language || 'N/A'}`);
      lines.push(`**Match Score:** ${code.score.toFixed(2)}`);
      lines.push(``);
      lines.push(`---`);
      lines.push(``);
    });

    return lines.join('\n');
  }
}
