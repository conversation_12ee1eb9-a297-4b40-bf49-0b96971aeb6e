import { retryFetch } from ".";

export async function fetchProxies(): Promise<string[]> {
  const proxyUrl =
    process.env.SCRAPINGDOG_PROXY_URL || "https://api.scrapingdog.com/scrape?api_key=6885f2d2d101144f10f05e59&dynamic=false&url=";
  const url =
    process.env.WEBSHARE_PROXIES_URL ||
    "https://proxy.webshare.io/api/v2/proxy/list/download/hmzhtupxjerljxbxqhvodrprihorkntgttbtbsdl/-/any/username/direct/-/";

  if (!proxyUrl || !url) {
    throw new Error("Missing required environment variables: SCRAPINGDOG_PROXY_URL or WEBSHARE_PROXIES_URL");
  }

  try {
    const response = await retryFetch(async () => fetch(`${proxyUrl}${url}`), { minTimeout: 1000 });

    if (!response.ok) {
      throw new Error(`Failed to fetch proxies: ${response.status} ${response.statusText}`);
    }

    const data = await response.text();

    const proxies = data
      .split("\n")
      .map(line => line.trim())
      .filter(line => line && line.includes(":"))
      .map(line => {
        const parts = line.split(":");
        if (parts.length >= 4) {
          const [ip, port, username, password] = parts;
          return `http://${username}:${password}@${ip}:${port}`;
        }
        return null;
      })
      .filter((proxy): proxy is string => proxy !== null);

    console.log(`Fetched ${proxies.length} proxies from Webshare`);
    return proxies;
  } catch (error) {
    console.error("Error fetching proxies from Webshare:", error);
    throw error;
  }
}
