# BlackHatWorld Tools

A comprehensive set of tools for searching, discovering, and scraping content from the BlackHatWorld forum. Built following KISS, DRY, and YAGNI principles.

## Features

- **Search Tool**: Advanced forum search with filtering and structured results
- **Scraper Tool**: Extract full article content from forum posts
- **Discovery Tool**: Find trending and popular threads based on engagement
- **Mock Data Support**: Use fixed test data during development to avoid API calls

## Tools Overview

### 1. BlackHatWorld Search Tool (`blackhatWorldSearchTool`)

Search the BlackHatWorld forum with advanced filtering options.

**Input Parameters:**
- `keywords` (required): Search terms
- `nodeNames` (optional): Forum categories to search in
- `newerThan` (optional): Start date (YYYY-MM-DD)
- `olderThan` (optional): End date (YYYY-MM-DD)
- `order` (optional): Sort order ('relevance', 'date', 'replies')
- `parseResults` (optional): Whether to parse structured results

**Output:**
- Search URL
- Structured results with title, author, replies, views, etc.
- Pagination information
- Available forum nodes

### 2. BlackHatWorld Scrape Tool (`blackhatWorldScrapeTool`)

Extract full content from individual forum posts.

**Input Parameters:**
- `url` (required): URL of the forum post to scrape

**Output:**
- Article title, content, author, and metadata
- Success status

### 3. BlackHatWorld Discover Tool (`blackhatWorldDiscoverTool`)

Discover popular and trending threads from multiple forums.

**Input Parameters:**
- `forumUrl` (optional): Specific forum URL to scrape
- `minReplies` (optional): Minimum reply threshold (default: 10)
- `usePopularForums` (optional): Use predefined popular forums

**Output:**
- List of threads with engagement metrics
- Forums scraped information

## Usage Examples

### Basic Search

```typescript
import { blackhatWorldSearchTool } from './tools/blacthatworld';

const result = await blackhatWorldSearchTool.execute({
  context: {
    keywords: 'affiliate marketing',
    order: 'replies',
    parseResults: true
  }
});

console.log('Found', result.results?.length, 'results');
```

### Advanced Search with Filtering

```typescript
const result = await blackhatWorldSearchTool.execute({
  context: {
    keywords: 'content farm',
    nodeNames: ['Making Money', 'Black Hat SEO'],
    newerThan: '2024-01-01',
    order: 'date',
    parseResults: true
  }
});
```

### Discover Popular Threads

```typescript
import { blackhatWorldDiscoverTool } from './tools/blacthatworld';

const threads = await blackhatWorldDiscoverTool.execute({
  context: {
    minReplies: 20,
    usePopularForums: true
  }
});

console.log('Discovered', threads.totalFound, 'popular threads');
```

### Scrape Article Content

```typescript
import { blackhatWorldScrapeTool } from './tools/blacthatworld';

const article = await blackhatWorldScrapeTool.execute({
  context: {
    url: 'https://www.blackhatworld.com/seo/example-post.123456/'
  }
});

if (article.success) {
  console.log('Content:', article.content);
}
```

## Development Mode

To avoid making unnecessary API calls during development, set the environment variable:

```bash
export USE_MOCK_DATA=true
# or
export NODE_ENV=development
```

This will use predefined mock data for testing and development.

## Available Forum Categories

The tools support searching in specific forum categories:

- **Making Money**: Affiliate programs, CPA, dropshipping, etc.
- **Black Hat SEO**: Advanced SEO techniques and tools
- **White Hat SEO**: Legitimate SEO strategies
- **Social Media**: Facebook, Instagram, YouTube, etc.
- **Programming & Web Design**: Technical discussions
- **The Marketplace**: Services and products

## Agent Integration

The tools are integrated with the BlackHatWorld Agent (`blackhatWorldAgent`) which provides intelligent assistance for forum research and analysis.

## File Structure

```
src/mastra/tools/blacthatworld/
├── index.ts          # Main tools export
├── search.ts         # Search functionality
├── scraper.ts        # Content scraping utilities
├── example.ts        # Usage examples
└── README.md         # This documentation
```

## Environment Variables

- `FIRECRAWL_API_KEY`: API key for FireCrawl service
- `USE_MOCK_DATA`: Set to 'true' to use mock data
- `NODE_ENV`: Set to 'development' for development mode

## Error Handling

All tools include comprehensive error handling:
- Network failures are gracefully handled
- Invalid URLs return appropriate error messages
- Parsing errors fall back to empty results
- All errors are logged for debugging

## Performance Considerations

- Uses caching through FireCrawl's `maxAge` parameter
- Mock data mode for development testing
- Efficient parsing with Cheerio
- Proxy support for rate limiting and reliability

## Contributing

When extending these tools:
1. Follow KISS principle - keep implementations simple
2. Apply DRY principle - reuse common functionality
3. Follow YAGNI principle - only add features when needed
4. Include proper TypeScript types
5. Add error handling and logging
6. Update examples and documentation
