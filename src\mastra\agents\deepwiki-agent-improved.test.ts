import { describe, it, expect, beforeAll } from "vitest";
import { deepWikiAgent } from "./deepwiki-agent";

// 设置测试环境
beforeAll(() => {
  process.env.NODE_ENV = "test";
});

describe("DeepWiki Agent Tests - Improved Version", () => {
  
  describe("Basic Functionality", () => {
    it("should respond to basic queries", async () => {
      const response = await deepWikiAgent.generate("Hello, can you help me search GitHub repositories?");
      
      expect(response).toBeDefined();
      expect(response.text).toBeDefined();
      expect(typeof response.text).toBe("string");
      expect(response.text.length).toBeGreaterThan(0);
      console.log("Basic response length:", response.text.length);
    }, 60000);

    it("should mention code search capabilities when asked", async () => {
      const response = await deepWikiAgent.generate(
        "What can you help me with regarding code search?"
      );
      
      expect(response).toBeDefined();
      expect(response.text).toBeDefined();
      expect(response.text.toLowerCase()).toContain("code");
      console.log("Code search response contains 'code':", response.text.toLowerCase().includes("code"));
    }, 60000);
  });

  describe("Simplified Interface Tests", () => {
    it("should understand simplified focus options", async () => {
      const response = await deepWikiAgent.generate(
        "What are the search focus options available in DeepWiki?"
      );
      
      expect(response).toBeDefined();
      const lowerResponse = response.text.toLowerCase();
      
      // 应该提到新的简化选项
      expect(
        lowerResponse.includes("code") || 
        lowerResponse.includes("docs") || 
        lowerResponse.includes("focus")
      ).toBe(true);
      
      console.log("Response mentions simplified options:", 
        lowerResponse.includes("code") || lowerResponse.includes("docs"));
    }, 60000);

    it("should explain batch search capabilities", async () => {
      const response = await deepWikiAgent.generate(
        "Can you search multiple repositories at once?"
      );
      
      expect(response).toBeDefined();
      const lowerResponse = response.text.toLowerCase();
      
      expect(
        lowerResponse.includes("batch") || 
        lowerResponse.includes("multiple") || 
        lowerResponse.includes("concurrent") ||
        lowerResponse.includes("parallel")
      ).toBe(true);
      
      console.log("Response mentions batch capabilities:", 
        lowerResponse.includes("batch") || lowerResponse.includes("multiple"));
    }, 60000);
  });

  describe("Tool Integration Tests", () => {
    it("should effectively use simplified DeepWiki search tool", async () => {
      const response = await deepWikiAgent.generate(
        "Search for React hooks patterns in the facebook/react repository with focus on code examples"
      );
      
      expect(response).toBeDefined();
      const lowerResponse = response.text.toLowerCase();
      expect(
        lowerResponse.includes("search") || 
        lowerResponse.includes("react") || 
        lowerResponse.includes("hooks") ||
        lowerResponse.includes("repository")
      ).toBe(true);
    }, 120000);

    it("should handle batch search requests", async () => {
      const response = await deepWikiAgent.generate(
        "I need to search for authentication patterns in both React and Next.js repositories. Can you do this efficiently?"
      );
      
      expect(response).toBeDefined();
      const lowerResponse = response.text.toLowerCase();
      expect(
        lowerResponse.includes("batch") ||
        lowerResponse.includes("multiple") ||
        lowerResponse.includes("concurrent") ||
        lowerResponse.includes("efficient") ||
        lowerResponse.includes("both")
      ).toBe(true);
    }, 120000);
  });

  describe("Performance Awareness Tests", () => {
    it("should understand performance benefits of new design", async () => {
      const response = await deepWikiAgent.generate(
        "What are the performance improvements in the new DeepWiki design?"
      );
      
      expect(response).toBeDefined();
      const lowerResponse = response.text.toLowerCase();
      
      // 应该提到并发、批量或性能改进
      expect(
        lowerResponse.includes("concurrent") ||
        lowerResponse.includes("batch") ||
        lowerResponse.includes("faster") ||
        lowerResponse.includes("performance") ||
        lowerResponse.includes("efficient")
      ).toBe(true);
      
      console.log("Mentions performance improvements:", 
        lowerResponse.includes("concurrent") || lowerResponse.includes("batch"));
    }, 60000);

    it("should recommend appropriate search strategies", async () => {
      const response = await deepWikiAgent.generate(
        "I need to analyze multiple repositories for similar patterns. What's the best approach?"
      );
      
      expect(response).toBeDefined();
      const lowerResponse = response.text.toLowerCase();
      
      // 应该建议使用批量搜索
      expect(
        lowerResponse.includes("batch") ||
        lowerResponse.includes("multiple") ||
        lowerResponse.includes("concurrent") ||
        lowerResponse.includes("efficient")
      ).toBe(true);
      
      console.log("Recommends efficient strategies:", 
        lowerResponse.includes("batch") || lowerResponse.includes("concurrent"));
    }, 60000);
  });
});
