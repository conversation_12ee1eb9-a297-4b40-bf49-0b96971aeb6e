/**
 * DeepWiki Client
 * 用于代码编辑时参考指定开源库的GitHub实现代码
 */

import { randomUUID } from "node:crypto";

// ============================================================================
// 类型定义
// ============================================================================

export interface DeepWikiQuery {
  engine_id: string;
  user_query: string;
  keywords: string[];
  repo_names: string[]; // DeepWiki API 仍然期望数组格式，但我们只传入单个仓库
  additional_context: string;
  query_id: string;
  use_notes: boolean;
  generate_summary: boolean;
}

export interface DeepWikiResponse {
  title?: string;
  queries: QueryItem[];
}

export interface QueryItem {
  user_query: string;
  use_knowledge: boolean;
  engine_id: string;
  repo_context_ids: string[];
  response: ResponseItem[];
  error: string | null;
  state: "pending" | "done";
  redis_stream: string | null;
}

export interface ResponseItem {
  type: "loading_indexes" | "chunk" | "reference" | "file_contents";
  data: any;
}

export interface QueryResult {
  type: string;
  data: any;
}

export interface FileReference {
  file_path: string;
  range_start: number;
  range_end: number;
}

export interface FileContent {
  repo: string;
  file_path: string;
  content: string;
}

// ============================================================================
// DeepWiki API 客户端
// ============================================================================

export class DeepWikiClient {
  private baseUrl = "https://api.devin.ai/ada";

  private getHeaders(): Record<string, string> {
    return {
      accept: "application/json",
      "accept-language": "zh-CN,zh;q=0.9",
      "content-type": "application/json",
      priority: "u=1, i",
      "sec-ch-ua": '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
      "sec-ch-ua-mobile": "?0",
      "sec-ch-ua-platform": '"macOS"',
      "sec-fetch-dest": "empty",
      "sec-fetch-mode": "cors",
      "sec-fetch-site": "cross-site"
    };
  }

  private generateQueryId(userQuery: string): string {
    const uuid = randomUUID();
    // 截断 query_id，参考 API 实现格式
    const truncatedQuery = userQuery.replace(/\s+/g, "-").toLowerCase().substring(0, 25);
    return `${truncatedQuery}_${uuid}`;
  }

  private createQueryPayload(userQuery: string, repoName: string, queryType: string = "general"): DeepWikiQuery {
    const queryId = this.generateQueryId(userQuery);

    // 根据查询类型调整 prompt
    let enhancedQuery = userQuery;
    const contextPrefix = `<relevant_context>This query was sent from the wiki page: Overview.</relevant_context>`;

    switch (queryType) {
      case "summary":
        enhancedQuery = `${contextPrefix}${userQuery} - Please provide a comprehensive summary with diagrams and charts where applicable`;
        break;
      case "implementation":
        enhancedQuery = `${contextPrefix}${userQuery} - Focus on specific code implementations and examples for reference`;
        break;
      case "architecture":
        enhancedQuery = `${contextPrefix}${userQuery} - Explain the architecture design and core features`;
        break;
      default:
        enhancedQuery = `${contextPrefix}${userQuery}`;
    }

    return {
      engine_id: "multihop",
      user_query: enhancedQuery,
      keywords: [],
      repo_names: [repoName], // 将单个仓库包装为数组
      additional_context: "",
      query_id: queryId,
      use_notes: false,
      generate_summary: queryType === "summary"
    };
  }

  async createQuery(userQuery: string, repoName: string, queryType: string = "general"): Promise<string> {
    if (!repoName) {
      throw new Error("Repository name is required");
    }

    // 每次都创建新的查询
    const queryPayload = this.createQueryPayload(userQuery, repoName, queryType);

    console.log("Sending query request", {
      queryId: queryPayload.query_id,
      repoName,
      queryType,
      userQuery: userQuery.substring(0, 50) + "..."
    });

    const response = await fetch(`${this.baseUrl}/query`, {
      method: "POST",
      headers: this.getHeaders(),
      body: JSON.stringify(queryPayload),
      mode: "cors",
      credentials: "omit"
    });

    if (!response.ok) {
      throw new Error(`Failed to send query: ${response.status} ${response.statusText}`);
    }

    return queryPayload.query_id;
  }

  async pollQuery(queryId: string, maxAttempts: number = 30, interval: number = 2000): Promise<DeepWikiResponse> {
    console.log(`Polling query ${queryId}`, { maxAttempts, interval });

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const response = await fetch(`${this.baseUrl}/query/${queryId}`, {
          method: "GET",
          headers: this.getHeaders(),
          mode: "cors",
          credentials: "omit"
        });

        if (!response.ok) {
          throw new Error(`Poll failed: ${response.status} ${response.statusText}`);
        }

        const data = (await response.json()) as DeepWikiResponse;

        // 检查是否有查询结果
        if (!data.queries || data.queries.length === 0) {
          console.log(`Poll attempt ${attempt}/${maxAttempts}`, {
            queriesCount: 0,
            hasQueries: false
          });
        } else {
          // 检查所有查询是否完成
          const completedQueries = data.queries.filter(q => q.state === "done");
          const hasErrors = data.queries.some(q => q.error);

          console.log(`Poll attempt ${attempt}/${maxAttempts}`, {
            totalQueries: data.queries.length,
            completedQueries: completedQueries.length,
            hasErrors
          });

          // 如果有错误，抛出异常
          if (hasErrors) {
            const errorQuery = data.queries.find(q => q.error);
            throw new Error(`Query error: ${errorQuery?.error}`);
          }

          // 如果所有查询都完成，返回结果
          if (completedQueries.length === data.queries.length && completedQueries.length > 0) {
            console.log(`Query ${queryId} completed after ${attempt} attempts`);
            return data;
          }
        }

        // 等待下次轮询
        if (attempt < maxAttempts) {
          await new Promise(resolve => setTimeout(resolve, interval));
        }
      } catch (error) {
        console.error(`Poll attempt ${attempt} failed:`, error);
        if (attempt === maxAttempts) {
          throw error;
        }
        await new Promise(resolve => setTimeout(resolve, interval));
      }
    }

    throw new Error(`Query ${queryId} did not complete within ${maxAttempts} attempts`);
  }

  async testQuery(queryId: string): Promise<DeepWikiResponse> {
    console.log(`Testing query ${queryId}`);

    const response = await fetch(`${this.baseUrl}/query/${queryId}`, {
      method: "GET",
      headers: this.getHeaders(),
      mode: "cors",
      credentials: "omit"
    });

    if (!response.ok) {
      throw new Error(`Test query failed: ${response.status} ${response.statusText}`);
    }

    const data = (await response.json()) as DeepWikiResponse;
    console.log(`Test query result`, {
      queriesCount: data.queries?.length || 0,
      hasQueries: !!data.queries
    });

    return data;
  }
}
