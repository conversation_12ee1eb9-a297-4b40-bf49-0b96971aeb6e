import {
  getAvailableProxy,
  recordProxyError,
  recordProxySuccess,
  getProxyStats,
  performHealthCheck,
  initializeProxyPool
} from './index';

// Simple example - just get and use a proxy
async function simpleExample() {
  // Get a proxy (automatically initializes pool on first use)
  const proxy = await getAvailableProxy();
  if (!proxy) {
    console.log('No healthy proxies available');
    return;
  }

  console.log('Got proxy:', proxy.replace(/\/\/.*@/, '//***@')); // Hide credentials

  try {
    // Use the proxy for your request
    // ... make HTTP request with proxy
    recordProxySuccess(proxy);
    console.log('Request successful');
  } catch (error) {
    recordProxyError(proxy);
    console.error('Request failed:', error);
  }
}

// Advanced example with custom configuration
async function advancedExample() {
  // Initialize with custom configuration
  await initializeProxyPool({
    refreshIntervalMs: 15 * 60 * 1000, // 15 minutes
    maxErrorCount: 3,
    healthCheckTimeoutMs: 3000
  });

  // Get a proxy for making requests
  const proxy = await getAvailableProxy();
  if (!proxy) {
    console.log('No healthy proxies available');
    return;
  }

  console.log('Using proxy:', proxy.replace(/\/\/.*@/, '//***@')); // Hide credentials

  try {
    // Make your request here (example with fetch)
    const response = await fetch('https://httpbin.org/ip', {
      // Note: You'll need to configure your HTTP client to use the proxy
      // This is just a conceptual example
    });

    if (response.ok) {
      recordProxySuccess(proxy);
      console.log('Request successful');
    } else {
      recordProxyError(proxy);
      console.log('Request failed with status:', response.status);
    }
  } catch (error) {
    recordProxyError(proxy);
    console.error('Request error:', error);
  }

  // Check proxy pool stats (optional)
  const stats = await getProxyStats();
  if (stats) {
    console.log('Proxy pool stats:', {
      total: stats.total,
      healthy: stats.healthy,
      unhealthy: stats.unhealthy,
      totalUsage: stats.totalUsage,
      totalErrors: stats.totalErrors
    });
  }

  // Perform health check manually (optional)
  await performHealthCheck();
}

// Helper function to make HTTP requests with proxy rotation
export async function makeRequestWithProxy(url: string, options: RequestInit = {}): Promise<Response> {
  const maxRetries = 3;
  let lastError: Error | null = null;

  for (let attempt = 0; attempt < maxRetries; attempt++) {
    const proxy = await getAvailableProxy();
    if (!proxy) {
      throw new Error('No healthy proxies available');
    }

    try {
      // Note: In a real implementation, you'd configure the proxy here
      // This might involve using a library like node-fetch with proxy support
      // or configuring an HTTP agent
      const response = await fetch(url, {
        ...options,
        // proxy configuration would go here
      });

      if (response.ok) {
        recordProxySuccess(proxy);
        return response;
      } else {
        recordProxyError(proxy);
        lastError = new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      recordProxyError(proxy);
      lastError = error as Error;
      console.warn(`Request failed with proxy ${proxy.replace(/\/\/.*@/, '//***@')}, attempt ${attempt + 1}:`, error);
    }
  }

  throw lastError || new Error('All proxy attempts failed');
}

// Run examples if this file is executed directly
if (require.main === module) {
  console.log('Running simple example...');
  simpleExample()
    .then(() => {
      console.log('\nRunning advanced example...');
      return advancedExample();
    })
    .catch(console.error);
}
