# BlackHatWorld Agent Testing Guide

这个文档描述了如何测试 BlackHatWorld Agent 的功能和性能。

## 🚀 快速开始

### 安装依赖

```bash
npm install
```

### 运行所有测试

```bash
npm test
```

### 运行特定测试

```bash
# 只运行 Agent 功能测试
npm run test:agent

# 只运行集成测试
npm run test:integration

# 运行测试并生成覆盖率报告
npm run test:coverage

# 监视模式（开发时使用）
npm run test:watch
```

## 📋 测试类型

### 1. 基础功能测试 (`blackhatworld-agent.test.ts`)

测试 Agent 的核心功能：

- **基本响应能力**：验证 Agent 能够响应基本查询
- **答案相关性**：使用 `AnswerRelevancyMetric` 评估回答质量
- **指令遵循**：使用 `PromptAlignmentMetric` 检查是否遵循特定指令
- **内容安全**：使用 `ToxicityMetric` 和 `BiasMetric` 确保输出安全
- **内容质量**：使用多种指标评估回答的完整性和一致性

### 2. 集成测试 (`blackhatworld-agent.integration.test.ts`)

测试 Agent 与工具的集成：

- **工具集成**：验证搜索和抓取工具的正确使用
- **性能基准**：测量响应时间和并发处理能力
- **错误处理**：测试各种边缘情况的处理
- **上下文记忆**：验证多轮对话的上下文保持

## 🎯 评估指标

### LLM 评估指标

- **AnswerRelevancyMetric**：评估回答与问题的相关性
- **PromptAlignmentMetric**：检查是否遵循特定指令
- **ToxicityMetric**：检测有害内容
- **BiasMetric**：检测偏见内容
- **SummarizationMetric**：评估总结质量

### NLP 评估指标

- **ContentSimilarityMetric**：测量内容相似性
- **ToneConsistencyMetric**：评估语调一致性
- **KeywordCoverageMetric**：检查关键词覆盖率
- **CompletenessMetric**：评估回答完整性

## 🔧 测试配置

### 环境变量

测试期间会自动设置以下环境变量：

```bash
USE_MOCK_DATA=true    # 使用模拟数据避免实际API调用
NODE_ENV=test         # 测试环境标识
```

### 配置文件

- `vitest.config.ts`：Vitest 测试框架配置
- `globalSetup.ts`：全局测试设置
- `testSetup.ts`：测试环境初始化

## 📊 测试结果解读

### 评分标准

大多数评估指标使用 0-1 分数：

- **0.8-1.0**：优秀
- **0.6-0.8**：良好
- **0.4-0.6**：一般
- **0.2-0.4**：需要改进
- **0.0-0.2**：差

### 特殊指标

- **ToxicityMetric** 和 **BiasMetric**：分数越低越好
- **性能测试**：响应时间应在合理范围内

## 🐛 故障排除

### 常见问题

1. **测试超时**
   ```bash
   # 增加超时时间
   vitest --testTimeout=120000
   ```

2. **API 限制**
   ```bash
   # 确保使用模拟数据
   export USE_MOCK_DATA=true
   ```

3. **依赖问题**
   ```bash
   # 重新安装依赖
   rm -rf node_modules package-lock.json
   npm install
   ```

### 调试模式

```bash
# 运行单个测试文件并显示详细输出
npx vitest run src/mastra/agents/blackhatworld-agent.test.ts --reporter=verbose

# 运行特定测试用例
npx vitest run -t "should provide relevant answers"
```

## 📈 持续集成

### GitHub Actions 示例

```yaml
name: Test BlackHatWorld Agent
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '20'
      - run: npm install
      - run: npm test
      - run: npm run test:coverage
```

## 🎯 测试最佳实践

1. **使用模拟数据**：避免实际API调用，提高测试速度和可靠性
2. **设置合理期望**：AI输出具有不确定性，设置合理的分数阈值
3. **测试边缘情况**：包括空输入、长输入、特殊字符等
4. **监控性能**：跟踪响应时间和资源使用
5. **定期更新**：随着Agent改进，更新测试用例和期望值

## 📝 添加新测试

### 创建新的评估测试

```typescript
it("should handle new functionality", async () => {
  const metric = new YourCustomMetric(evalModel);
  
  const result = await evaluate(
    blackhatWorldAgent,
    "Your test query",
    metric
  );

  expect(result.score).toBeGreaterThan(0.7);
  console.log("Test Score:", result.score);
});
```

### 创建性能测试

```typescript
it("should perform within time limits", async () => {
  const startTime = Date.now();
  
  const response = await blackhatWorldAgent.generate("Your query");
  
  const duration = Date.now() - startTime;
  expect(duration).toBeLessThan(30000); // 30秒
});
```

## 🔗 相关资源

- [Mastra Evals 文档](https://docs.mastra.ai/docs/evals/overview)
- [Vitest 文档](https://vitest.dev/)
- [BlackHatWorld Agent 源码](./src/mastra/agents/blackhatworld-agent.ts)
