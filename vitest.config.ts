import { defineConfig } from "vitest/config";

export default defineConfig({
  test: {
    globalSetup: "./globalSetup.ts",
    setupFiles: ["./testSetup.ts"],
    environment: "node",
    testTimeout: 120000, // 增加到2分钟，因为AI评估可能较慢
    hookTimeout: 60000,
    teardownTimeout: 10000,
    // 并发运行测试可能会导致API限制问题，所以设置为false
    pool: "forks",
    poolOptions: {
      forks: {
        singleFork: true
      }
    }
  }
});
