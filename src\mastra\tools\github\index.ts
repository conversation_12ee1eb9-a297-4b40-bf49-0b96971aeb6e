/**
 * GitHub Tools for Mastra
 * Simple interface for repository and code search with batch processing support
 */

import { z } from "zod";
import { GitHubClient, GitHubSearchParams } from "./client.js";
import { GitHubParser } from "./parser.js";
import { createTool } from "@mastra/core/tools";

// ============================================================================
// Tool Definitions - Simple Design
// ============================================================================

// Single search schema
const singleSearchSchema = z.object({
  query: z
    .string()
    .describe("Search query. Examples: 'react hooks', 'authentication middleware', 'machine learning python'"),
  searchType: z
    .enum(["repositories", "code"])
    .describe("Search type: 'repositories' for GitHub repos, 'code' for code files and content"),
  user: z.string().optional().describe("Limit search to specific user (e.g., 'facebook')"),
  org: z.string().optional().describe("Limit search to specific organization"),
  repo: z.string().optional().describe("Limit search to specific repository (format: 'owner/repo')"),
  language: z.string().optional().describe("Programming language filter (e.g., 'javascript', 'python')"),
  stars: z.string().optional().describe("Star count filter (e.g., '>1000', '100..500')"),
  forks: z.string().optional().describe("Fork count filter (e.g., '>50')"),
  topics: z.array(z.string()).optional().describe("Repository topics (only for repository search)"),
  license: z.string().optional().describe("License filter (only for repository search)"),
  sort: z.string().optional().describe("Sort by: 'stars', 'forks', 'updated', 'best-match'"),
  order: z.enum(["asc", "desc"]).optional().describe("Sort order"),
  limit: z.number().min(1).max(100).default(30).describe("Number of results to return (max 100)")
});

// Batch search schema
const batchSearchSchema = z.object({
  queries: z
    .array(
      z.object({
        query: z.string().describe("Search query"),
        searchType: z.enum(["repositories", "code"]).describe("Search type"),
        user: z.string().optional(),
        org: z.string().optional(),
        repo: z.string().optional(),
        language: z.string().optional(),
        stars: z.string().optional(),
        forks: z.string().optional(),
        topics: z.array(z.string()).optional(),
        license: z.string().optional(),
        sort: z.string().optional(),
        order: z.enum(["asc", "desc"]).optional(),
        limit: z.number().min(1).max(100).default(30).optional()
      })
    )
    .min(1)
    .max(5)
    .describe("Array of search queries (max 5 for performance)"),
  concurrent: z
    .boolean()
    .default(true)
    .describe("Whether to run queries concurrently for faster results")
});

// Output schemas
const searchResultSchema = z.object({
  success: z.boolean().describe("Whether the search was successful"),
  query: z.string().describe("The search query that was used"),
  searchType: z.string().describe("The type of search performed"),
  totalCount: z.number().describe("Total number of results found"),
  resultCount: z.number().describe("Number of results returned"),
  summary: z.string().describe("Summary of search results"),
  content: z.string().describe("Formatted markdown content"),
  repositories: z.array(z.object({
    id: z.number(),
    name: z.string(),
    fullName: z.string(),
    description: z.string().nullable(),
    url: z.string(),
    language: z.string().nullable(),
    stars: z.number(),
    forks: z.number(),
    topics: z.array(z.string()),
    owner: z.object({
      login: z.string(),
      type: z.string()
    })
  })).optional().describe("Repository results (if searchType is 'repositories')"),
  codeResults: z.array(z.object({
    fileName: z.string(),
    filePath: z.string(),
    htmlUrl: z.string(),
    repository: z.object({
      name: z.string(),
      fullName: z.string(),
      url: z.string(),
      stars: z.number(),
      language: z.string().nullable()
    }),
    score: z.number()
  })).optional().describe("Code results (if searchType is 'code')"),
  executionTime: z.number().describe("Execution time in milliseconds"),
  error: z.string().optional().describe("Error message if search failed")
});

const batchSearchOutputSchema = z.object({
  results: z.array(searchResultSchema).describe("Array of search results"),
  totalExecutionTime: z.number().describe("Total execution time in milliseconds"),
  successCount: z.number().describe("Number of successful searches"),
  failureCount: z.number().describe("Number of failed searches")
});

// ============================================================================
// Helper Functions
// ============================================================================

// Execute single search
async function executeSingleSearch(
  query: string,
  searchType: "repositories" | "code",
  options: any = {}
): Promise<z.infer<typeof searchResultSchema>> {
  const client = new GitHubClient();
  const parser = new GitHubParser();
  const startTime = Date.now();

  try {
    const searchParams: GitHubSearchParams = {
      query,
      searchType,
      ...options,
      per_page: options.limit || 30
    };

    const response = await client.search(searchParams);
    const parsed = parser.parseResponse(response, searchType);
    const executionTime = Date.now() - startTime;

    return {
      success: true,
      query,
      searchType,
      totalCount: parsed.totalCount,
      resultCount: searchType === "repositories" 
        ? (parsed.repositories?.length || 0)
        : (parsed.codeResults?.length || 0),
      summary: parsed.summary,
      content: parsed.markdown,
      repositories: parsed.repositories,
      codeResults: parsed.codeResults,
      executionTime
    };

  } catch (error) {
    const executionTime = Date.now() - startTime;
    console.error(`❌ GitHub search failed for ${searchType}:`, error);

    return {
      success: false,
      query,
      searchType,
      totalCount: 0,
      resultCount: 0,
      summary: "Search failed",
      content: "",
      executionTime,
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
}

// ============================================================================
// Tool Implementations
// ============================================================================

// Single search tool
export const githubSearchTool = createTool({
  id: "github-search",
  description: "Search GitHub repositories or code with flexible filtering options. Supports both repository discovery and code pattern searching.",
  inputSchema: singleSearchSchema,
  outputSchema: searchResultSchema,
  execute: async ({ context }) => {
    const { query, searchType, limit, ...options } = context;
    return await executeSingleSearch(query, searchType, { ...options, limit });
  }
});

// Batch search tool
export const githubBatchSearchTool = createTool({
  id: "github-batch-search",
  description: "Search multiple GitHub queries concurrently for faster results. Maximum 5 queries per batch.",
  inputSchema: batchSearchSchema,
  outputSchema: batchSearchOutputSchema,
  execute: async ({ context }) => {
    const { queries, concurrent } = context;
    const startTime = Date.now();

    console.log(`🚀 Starting ${concurrent ? 'concurrent' : 'sequential'} GitHub batch search for ${queries.length} queries`);

    let results: z.infer<typeof searchResultSchema>[];

    if (concurrent) {
      // Execute all queries concurrently
      results = await Promise.all(
        queries.map(({ query, searchType, limit, ...options }) =>
          executeSingleSearch(query, searchType, { ...options, limit })
        )
      );
    } else {
      // Execute queries sequentially
      results = [];
      for (const { query, searchType, limit, ...options } of queries) {
        const result = await executeSingleSearch(query, searchType, { ...options, limit });
        results.push(result);
      }
    }

    const totalExecutionTime = Date.now() - startTime;
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.length - successCount;

    console.log(`✅ GitHub batch search completed: ${successCount} success, ${failureCount} failures in ${totalExecutionTime}ms`);

    return {
      results,
      totalExecutionTime,
      successCount,
      failureCount
    };
  }
});
