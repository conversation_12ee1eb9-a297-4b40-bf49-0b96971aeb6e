/**
 * GitHub Tools for Mastra
 * 专注于高质量仓库搜索，自动过滤优质项目（>100星，活跃维护）
 * 简化接口，移除过度复杂的参数，内置最佳实践过滤条件
 */

import { z } from "zod";
import { GitHubClient, GitHubSearchParams } from "./client.js";
import { GitHubParser } from "./parser.js";
import { createTool } from "@mastra/core/tools";

// ============================================================================
// Tool Definitions - Simple Design
// ============================================================================

// Single search schema - 简化为高质量搜索
const singleSearchSchema = z.object({
  query: z.string().describe("Search query. Examples: 'react hooks', 'authentication middleware', 'machine learning python'"),
  searchType: z
    .enum(["repositories", "code"])
    .describe("Search type: 'repositories' for high-quality GitHub repos, 'code' for code examples"),
  language: z.string().optional().describe("Programming language filter (e.g., 'javascript', 'python', 'typescript')"),
  stars: z.string().optional().describe("Star count filter (e.g., '>100', '100..500')"),
  createdDays
  created: z.string().optional().describe("Created date filter (e.g., '>2020-01-01', '2020-01-01..2023-01-01')"),
  pushed: z.string().optional().describe("Pushed date filter (e.g., '>2023-01-01', '2023-01-01..2025-01-01')"),
  limit: z.number().min(1).max(50).default(20).describe("Number of results to return (max 50, default 20)")
});

// Batch search schema
const batchSearchSchema = z.object({
  queries: z
    .array(
      z.object({
        query: z.string().describe("Search query"),
        searchType: z.enum(["repositories", "code"]).describe("Search type"),
        language: z.string().optional().describe("Programming language filter"),
        stars: z.string().optional().describe("Star count filter (e.g., '>100', '100..500')"),
        created: z.string().optional().describe("Created date filter (e.g., '>2020-01-01', '2020-01-01..2023-01-01')"),
        pushed: z.string().optional().describe("Pushed date filter (e.g., '>2023-01-01', '2023-01-01..2025-01-01')"),
        limit: z.number().min(1).max(50).default(20).optional()
      })
    )
    .min(1)
    .max(5)
    .describe("Array of search queries (max 5 for performance)"),
  concurrent: z.boolean().default(true).describe("Whether to run queries concurrently for faster results")
});

// Output schemas
const searchResultSchema = z.object({
  success: z.boolean().describe("Whether the search was successful"),
  query: z.string().describe("The search query that was used"),
  searchType: z.string().describe("The type of search performed"),
  totalCount: z.number().describe("Total number of results found"),
  resultCount: z.number().describe("Number of results returned"),
  summary: z.string().describe("Summary of search results"),
  content: z.string().describe("Formatted markdown content"),
  repositories: z
    .array(
      z.object({
        id: z.number(),
        name: z.string(),
        fullName: z.string(),
        description: z.string().nullable(),
        url: z.string(),
        language: z.string().nullable(),
        stars: z.number(),
        forks: z.number(),
        topics: z.array(z.string()),
        owner: z.object({
          login: z.string(),
          type: z.string()
        })
      })
    )
    .optional()
    .describe("Repository results (if searchType is 'repositories')"),
  codeResults: z
    .array(
      z.object({
        fileName: z.string(),
        filePath: z.string(),
        htmlUrl: z.string(),
        repository: z.object({
          name: z.string(),
          fullName: z.string(),
          url: z.string(),
          stars: z.number(),
          language: z.string().nullable()
        }),
        score: z.number()
      })
    )
    .optional()
    .describe("Code results (if searchType is 'code')"),
  executionTime: z.number().describe("Execution time in milliseconds"),
  error: z.string().optional().describe("Error message if search failed")
});

const batchSearchOutputSchema = z.object({
  results: z.array(searchResultSchema).describe("Array of search results"),
  totalExecutionTime: z.number().describe("Total execution time in milliseconds"),
  successCount: z.number().describe("Number of successful searches"),
  failureCount: z.number().describe("Number of failed searches")
});

// ============================================================================
// Helper Functions
// ============================================================================

// Execute single search with high-quality defaults
async function executeSingleSearch(
  query: string,
  searchType: "repositories" | "code",
  options: any = {}
): Promise<z.infer<typeof searchResultSchema>> {
  const client = new GitHubClient();
  const parser = new GitHubParser();
  const startTime = Date.now();

  try {
    // 构建高质量搜索参数
    const searchParams: GitHubSearchParams = {
      query,
      searchType,
      language: options.language,
      created: options.created,
      pushed: options.pushed,
      per_page: options.limit || 20,
      // 固定的高质量过滤参数
      stars: ">100", // 至少100星，确保质量
      created: ">2020-01-01", // 相对较新的项目
      pushed: ">2023-01-01", // 最近有维护
      sort: "stars", // 按星数排序
      order: "desc"
    };

    // 对于仓库搜索，添加额外的质量过滤
    if (searchType === "repositories") {
      // 在 readme 和 description 中搜索最佳实践相关关键词
      searchParams.is = ["public"]; // 只搜索公开仓库
    }

    const response = await client.search(searchParams);
    const parsed = parser.parseResponse(response, searchType);
    const executionTime = Date.now() - startTime;

    return {
      success: true,
      query,
      searchType,
      totalCount: parsed.totalCount,
      resultCount: searchType === "repositories" ? parsed.repositories?.length || 0 : parsed.codeResults?.length || 0,
      summary: parsed.summary,
      content: parsed.markdown,
      repositories: parsed.repositories,
      codeResults: parsed.codeResults,
      executionTime
    };
  } catch (error) {
    const executionTime = Date.now() - startTime;
    console.error(`❌ GitHub search failed for ${searchType}:`, error);

    return {
      success: false,
      query,
      searchType,
      totalCount: 0,
      resultCount: 0,
      summary: "Search failed",
      content: "",
      executionTime,
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
}

// ============================================================================
// Tool Implementations
// ============================================================================

// Single search tool
export const githubSearchTool = createTool({
  id: "github-search",
  description:
    "Search for high-quality GitHub repositories (>100 stars, actively maintained since 2023) or code examples. Automatically filters for quality projects.",
  inputSchema: singleSearchSchema,
  outputSchema: searchResultSchema,
  execute: async ({ context }) => {
    const { query, searchType, limit, ...options } = context;
    return await executeSingleSearch(query, searchType, { ...options, limit });
  }
});

// Batch search tool
export const githubBatchSearchTool = createTool({
  id: "github-batch-search",
  description:
    "Search multiple high-quality GitHub repositories or code patterns concurrently. Maximum 5 queries per batch for efficient comparison.",
  inputSchema: batchSearchSchema,
  outputSchema: batchSearchOutputSchema,
  execute: async ({ context }) => {
    const { queries, concurrent } = context;
    const startTime = Date.now();

    console.log(`🚀 Starting ${concurrent ? "concurrent" : "sequential"} GitHub batch search for ${queries.length} queries`);

    let results: z.infer<typeof searchResultSchema>[];

    if (concurrent) {
      // Execute all queries concurrently
      results = await Promise.all(
        queries.map(({ query, searchType, limit, ...options }) => executeSingleSearch(query, searchType, { ...options, limit }))
      );
    } else {
      // Execute queries sequentially
      results = [];
      for (const { query, searchType, limit, ...options } of queries) {
        const result = await executeSingleSearch(query, searchType, { ...options, limit });
        results.push(result);
      }
    }

    const totalExecutionTime = Date.now() - startTime;
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.length - successCount;

    console.log(`✅ GitHub batch search completed: ${successCount} success, ${failureCount} failures in ${totalExecutionTime}ms`);

    return {
      results,
      totalExecutionTime,
      successCount,
      failureCount
    };
  }
});
