import { ProxyAgent } from "undici"
const proxy = new ProxyAgent('http://127.0.0.1:7890')
fetch("https://www.blackhatworld.com/search/search", {
  dispatcher: proxy,
  "headers": {
    "accept": "application/json",
    "accept-language": "zh-CN,zh;q=0.9",
    "content-type": "multipart/form-data; boundary=----WebKitFormBoundary2BqZCtnNCHqa5GWJ",
    "priority": "u=1, i",
    "sec-ch-ua": "\"Chromium\";v=\"140\", \"Not=A?Brand\";v=\"24\", \"Google Chrome\";v=\"140\"",
    "sec-ch-ua-arch": "\"x86\"",
    "sec-ch-ua-bitness": "\"64\"",
    "sec-ch-ua-full-version": "\"140.0.7322.0\"",
    "sec-ch-ua-full-version-list": "\"Chromium\";v=\"140.0.7322.0\", \"Not=A?Brand\";v=\"********\", \"Google Chrome\";v=\"140.0.7322.0\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-model": "\"\"",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-ch-ua-platform-version": "\"10.0.0\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "x-requested-with": "XMLHttpRequest",
    "cookie": "_ga=GA1.1.968538537.1753585988; xf_notice_dismiss=-1; xf_csrf=yOuet-nPGO8IV81i; xf_session=H1v4nagRjv5dVDZeWkaiQ59FvAi33GID; _ga_VH2PZEKYEE=GS2.1.s1753754497$o6$g1$t1753754827$j31$l0$h0",
    "Referer": "https://www.blackhatworld.com/search/?type=post"
  },
  "body": "------WebKitFormBoundary2BqZCtnNCHqa5GWJ\r\nContent-Disposition: form-data; name=\"_xfToken\"\r\n\r\n1753754817,2c10c3b0b846c6f0d9a352b274ed80fb\r\n------WebKitFormBoundary2BqZCtnNCHqa5GWJ\r\nContent-Disposition: form-data; name=\"keywords\"\r\n\r\ncontent farm\r\n------WebKitFormBoundary2BqZCtnNCHqa5GWJ\r\nContent-Disposition: form-data; name=\"c[users]\"\r\n\r\n\r\n------WebKitFormBoundary2BqZCtnNCHqa5GWJ\r\nContent-Disposition: form-data; name=\"c[newer_than]\"\r\n\r\n\r\n------WebKitFormBoundary2BqZCtnNCHqa5GWJ\r\nContent-Disposition: form-data; name=\"c[older_than]\"\r\n\r\n\r\n------WebKitFormBoundary2BqZCtnNCHqa5GWJ\r\nContent-Disposition: form-data; name=\"c[min_reply_count]\"\r\n\r\n0\r\n------WebKitFormBoundary2BqZCtnNCHqa5GWJ\r\nContent-Disposition: form-data; name=\"c[child_nodes]\"\r\n\r\n1\r\n------WebKitFormBoundary2BqZCtnNCHqa5GWJ\r\nContent-Disposition: form-data; name=\"order\"\r\n\r\nrelevance\r\n------WebKitFormBoundary2BqZCtnNCHqa5GWJ\r\nContent-Disposition: form-data; name=\"search_type\"\r\n\r\npost\r\n------WebKitFormBoundary2BqZCtnNCHqa5GWJ\r\nContent-Disposition: form-data; name=\"_xfResponseType\"\r\n\r\njson\r\n------WebKitFormBoundary2BqZCtnNCHqa5GWJ\r\nContent-Disposition: form-data; name=\"_xfWithData\"\r\n\r\n1\r\n------WebKitFormBoundary2BqZCtnNCHqa5GWJ\r\nContent-Disposition: form-data; name=\"_xfRequestUri\"\r\n\r\n/search/?type=post\r\n------WebKitFormBoundary2BqZCtnNCHqa5GWJ\r\nContent-Disposition: form-data; name=\"_xfToken\"\r\n\r\n1753754817,2c10c3b0b846c6f0d9a352b274ed80fb\r\n------WebKitFormBoundary2BqZCtnNCHqa5GWJ--\r\n",
  "method": "POST"
}).then(res => {
  return res.json()
}).then(data => {
  console.log('data:', data)
}).catch(err => {
  console.log('err:', err)
})