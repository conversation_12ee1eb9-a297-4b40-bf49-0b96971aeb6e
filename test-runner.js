#!/usr/bin/env node

/**
 * BlackHatWorld Agent Test Runner
 * 简单的测试运行脚本，用于验证测试环境设置
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🚀 BlackHatWorld Agent Test Runner\n');

// 设置环境变量
process.env.USE_MOCK_DATA = 'true';
process.env.NODE_ENV = 'test';

console.log('📋 Environment Setup:');
console.log(`   USE_MOCK_DATA: ${process.env.USE_MOCK_DATA}`);
console.log(`   NODE_ENV: ${process.env.NODE_ENV}`);
console.log('');

// 检查依赖
console.log('🔍 Checking dependencies...');

const checkDependency = (packageName) => {
  try {
    require.resolve(packageName);
    console.log(`   ✅ ${packageName} - installed`);
    return true;
  } catch (error) {
    console.log(`   ❌ ${packageName} - missing`);
    return false;
  }
};

const requiredDeps = [
  'vitest',
  '@mastra/evals',
  '@mastra/core'
];

const missingDeps = requiredDeps.filter(dep => !checkDependency(dep));

if (missingDeps.length > 0) {
  console.log('\n❌ Missing dependencies. Please run:');
  console.log('   npm install');
  process.exit(1);
}

console.log('\n✅ All dependencies found');

// 运行测试的函数
const runTest = (testCommand, description) => {
  return new Promise((resolve, reject) => {
    console.log(`\n🧪 Running ${description}...`);
    console.log(`   Command: ${testCommand}`);
    
    const [command, ...args] = testCommand.split(' ');
    const child = spawn(command, args, {
      stdio: 'inherit',
      cwd: __dirname,
      env: { ...process.env }
    });

    child.on('close', (code) => {
      if (code === 0) {
        console.log(`   ✅ ${description} completed successfully`);
        resolve();
      } else {
        console.log(`   ❌ ${description} failed with code ${code}`);
        reject(new Error(`Test failed with code ${code}`));
      }
    });

    child.on('error', (error) => {
      console.log(`   ❌ ${description} error:`, error.message);
      reject(error);
    });
  });
};

// 主测试流程
async function runTests() {
  try {
    console.log('\n🎯 Starting BlackHatWorld Agent Tests');
    console.log('=' .repeat(50));

    // 运行基础功能测试
    await runTest(
      'npx vitest run src/mastra/agents/blackhatworld-agent.test.ts --reporter=verbose',
      'Basic Functionality Tests'
    );

    // 运行集成测试
    await runTest(
      'npx vitest run src/mastra/agents/blackhatworld-agent.integration.test.ts --reporter=verbose',
      'Integration Tests'
    );

    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📊 Test Summary:');
    console.log('   ✅ Basic functionality tests passed');
    console.log('   ✅ Integration tests passed');
    console.log('   ✅ Agent is ready for use');

  } catch (error) {
    console.log('\n❌ Test execution failed:', error.message);
    console.log('\n🔧 Troubleshooting tips:');
    console.log('   1. Make sure all dependencies are installed: npm install');
    console.log('   2. Check that environment variables are set correctly');
    console.log('   3. Verify that the Mastra configuration is valid');
    console.log('   4. Check the test logs above for specific error details');
    
    process.exit(1);
  }
}

// 处理命令行参数
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
  console.log('BlackHatWorld Agent Test Runner');
  console.log('');
  console.log('Usage:');
  console.log('  node test-runner.js [options]');
  console.log('');
  console.log('Options:');
  console.log('  --help, -h     Show this help message');
  console.log('  --basic        Run only basic functionality tests');
  console.log('  --integration  Run only integration tests');
  console.log('  --coverage     Run tests with coverage report');
  console.log('');
  console.log('Examples:');
  console.log('  node test-runner.js');
  console.log('  node test-runner.js --basic');
  console.log('  node test-runner.js --coverage');
  process.exit(0);
}

if (args.includes('--basic')) {
  runTest(
    'npx vitest run src/mastra/agents/blackhatworld-agent.test.ts --reporter=verbose',
    'Basic Functionality Tests'
  ).then(() => {
    console.log('\n✅ Basic tests completed');
  }).catch((error) => {
    console.log('\n❌ Basic tests failed:', error.message);
    process.exit(1);
  });
} else if (args.includes('--integration')) {
  runTest(
    'npx vitest run src/mastra/agents/blackhatworld-agent.integration.test.ts --reporter=verbose',
    'Integration Tests'
  ).then(() => {
    console.log('\n✅ Integration tests completed');
  }).catch((error) => {
    console.log('\n❌ Integration tests failed:', error.message);
    process.exit(1);
  });
} else if (args.includes('--coverage')) {
  runTest(
    'npx vitest run --coverage',
    'All Tests with Coverage'
  ).then(() => {
    console.log('\n✅ Tests with coverage completed');
  }).catch((error) => {
    console.log('\n❌ Tests with coverage failed:', error.message);
    process.exit(1);
  });
} else {
  // 运行所有测试
  runTests();
}
