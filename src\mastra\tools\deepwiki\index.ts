/**
 * DeepWiki Tool - GitHub repository search via DeepWiki API
 */

import { z } from "zod";
import { DeepWikiClient } from "./client";
import { DeepWikiParser } from "./parser";
import { createTool } from "@mastra/core/tools";

// Helper function to get language from file path
function getLanguageFromPath(filePath: string): string {
  const ext = filePath.split(".").pop()?.toLowerCase() || "";
  const languageMap: Record<string, string> = {
    js: "javascript",
    jsx: "javascript",
    ts: "typescript",
    tsx: "typescript",
    py: "python",
    java: "java",
    cpp: "cpp",
    c: "c",
    cs: "csharp",
    php: "php",
    rb: "ruby",
    go: "go",
    rs: "rust",
    swift: "swift",
    kt: "kotlin",
    scala: "scala",
    sh: "bash",
    yml: "yaml",
    yaml: "yaml",
    json: "json",
    xml: "xml",
    html: "html",
    css: "css",
    scss: "scss",
    sass: "sass",
    md: "markdown",
    sql: "sql"
  };
  return languageMap[ext] || ext || "text";
}

export function registerDeepWikiTool(server: McpServer) {
  const deepwikiClient = new DeepWikiClient();
  const parser = new DeepWikiParser();

  server.tool(
    "search-code",
    "Use DeepWiki to search for code implementations in a GitHub repository. Perfect for finding real-world examples and best practices. Provide detailed, specific queries for best results.",
    {
      query: z
        .string()
        .describe(
          "Detailed DeepWiki search query: Be specific about what you're looking for. Examples: 'how to implement user authentication with JWT tokens', 'database connection pooling setup', 'React component lifecycle methods', 'error handling patterns in async functions'"
        ),
      repository: z
        .string()
        .describe("GitHub repository name to search in (format: 'owner/repo', e.g., 'microsoft/playwright', 'vercel/next.js')"),
      queryType: z
        .enum(["summary", "implementation", "architecture", "general"])
        .optional()
        .describe(
          "Query type: 'summary' for comprehensive overview, 'implementation' for specific code examples, 'architecture' for design patterns and structure, 'general' for mixed content (default)"
        )
    },
    async ({ query, repository, queryType = "general" }) => {
      try {
        // 创建查询
        const queryId = await deepwikiClient.createQuery(query, repository, queryType);

        // 轮询结果
        const maxAttempts = Math.ceil(90 / 2); // 每2秒轮询一次
        const response = await deepwikiClient.pollQuery(queryId, maxAttempts, 2000);

        // 解析结果
        const result = parser.parseResponse(response);

        // 根据查询类型返回不同格式的内容
        if (queryType === "summary" && result.combinedContent) {
          // 总结性查询：返回拼接的总结内容，便于大模型理解
          return {
            content: [
              {
                type: "text",
                text: `# ${repository} - ${query}\n\n${result.combinedContent}\n\n---\n*查询ID: ${queryId}*`
              }
            ]
          };
        } else if (queryType === "implementation" && result.codeSnippets.length > 0) {
          // 实现性查询：重点展示代码片段
          return {
            content: [
              {
                type: "text",
                text: `# ${repository} 中的 "${query}" 实现\n\n${result.codeSnippets
                  .map(
                    (snippet, i) =>
                      `## ${i + 1}. ${snippet.filePath}\n**行数**: ${snippet.startLine}-${snippet.endLine}\n\n\`\`\`${getLanguageFromPath(snippet.filePath)}\n${snippet.content}\n\`\`\`\n`
                  )
                  .join("\n")}\n\n---\n*查询ID: ${queryId}*`
              }
            ]
          };
        } else {
          // 通用查询：平衡展示总结和代码
          const sections = [];

          if (result.combinedContent) {
            sections.push(result.combinedContent);
          }

          if (result.codeSnippets.length > 0) {
            sections.push("\n## 代码示例\n");
            result.codeSnippets.slice(0, 3).forEach((snippet, i) => {
              sections.push(
                `### ${i + 1}. ${snippet.filePath}\n\`\`\`${getLanguageFromPath(snippet.filePath)}\n${snippet.content}\n\`\`\`\n`
              );
            });

            if (result.codeSnippets.length > 3) {
              sections.push(`*还有 ${result.codeSnippets.length - 3} 个代码片段...*`);
            }
          }

          return {
            content: [
              {
                type: "text",
                text: `# ${repository} - ${query}\n\n${sections.join("\n")}\n\n---\n*查询ID: ${queryId}*`
              }
            ]
          };
        }
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `搜索失败: ${error instanceof Error ? error.message : "未知错误"}`
            }
          ],
          isError: true
        };
      }
    }
  );
}

export const deepwikiTool = createTool({
  id: "deepwiki-search",
  description:
    "Use DeepWiki to search for code implementations in a GitHub repository. Perfect for finding real-world examples and best practices. Provide detailed, specific queries for best results.",
  inputSchema: z.object({
    query: z.string(),
    repository: z.string(),
    queryType: z.enum(["summary", "implementation", "architecture", "general"]).optional()
  }),
  outputSchema: z.object({
    content: z.array(
      z.object({
        type: z.string(),
        text: z.string()
      })
    )
  })
});
