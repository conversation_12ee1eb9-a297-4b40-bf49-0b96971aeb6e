/**
 * DeepWiki Tools for Mastra
 * 用于代码编辑时参考指定开源库的GitHub实现代码
 */

import { z } from "zod";
import { DeepWikiClient } from "./client.js";
import { DeepWikiParser } from "./parser.js";
import { createTool } from "@mastra/core/tools";

// ============================================================================
// 工具定义
// ============================================================================

const searchCodeSchema = z.object({
  query: z
    .string()
    .describe(
      "Detailed DeepWiki search query: Be specific about what you're looking for. Examples: 'how to implement user authentication with JWT tokens', 'database connection pooling setup', 'React component lifecycle methods', 'error handling patterns in async functions'"
    ),
  repository: z
    .string()
    .describe("GitHub repository name to search in (format: 'owner/repo', e.g., 'microsoft/playwright', 'vercel/next.js')"),
  queryType: z
    .enum(["summary", "implementation", "architecture", "general"])
    .default("general")
    .describe(
      "Query type: 'summary' for comprehensive overview, 'implementation' for specific code examples, 'architecture' for design patterns and structure, 'general' for mixed content (default)"
    )
});

const searchCodeOutputSchema = z.object({
  success: z.boolean().describe("Whether the search was successful"),
  repository: z.string().describe("The repository that was searched"),
  query: z.string().describe("The search query that was used"),
  queryType: z.string().describe("The type of query that was performed"),
  summary: z.string().describe("A summary of the search results"),
  content: z.string().describe("The formatted markdown content with code snippets"),
  combinedContent: z.string().describe("Combined content for AI processing"),
  stats: z
    .object({
      chunks: z.number().describe("Number of text chunks found"),
      references: z.number().describe("Number of file references found"),
      fileContents: z.number().describe("Number of file contents retrieved"),
      codeSnippets: z.number().describe("Number of code snippets extracted")
    })
    .describe("Statistics about the search results"),
  codeSnippets: z
    .array(
      z.object({
        repo: z.string().describe("Repository name"),
        filePath: z.string().describe("File path within the repository"),
        content: z.string().describe("The actual code content"),
        startLine: z.number().describe("Starting line number"),
        endLine: z.number().describe("Ending line number"),
        language: z.string().describe("Programming language detected")
      })
    )
    .describe("Array of extracted code snippets"),
  error: z.string().optional().describe("Error message if the search failed")
});

// Helper function to get language from file path
function getLanguageFromPath(filePath: string): string {
  const ext = filePath.split(".").pop()?.toLowerCase() || "";
  const languageMap: Record<string, string> = {
    js: "javascript",
    jsx: "javascript",
    ts: "typescript",
    tsx: "typescript",
    py: "python",
    java: "java",
    cpp: "cpp",
    c: "c",
    cs: "csharp",
    php: "php",
    rb: "ruby",
    go: "go",
    rs: "rust",
    swift: "swift",
    kt: "kotlin",
    scala: "scala",
    sh: "bash",
    yml: "yaml",
    yaml: "yaml",
    json: "json",
    xml: "xml",
    html: "html",
    css: "css",
    scss: "scss",
    sass: "sass",
    md: "markdown",
    sql: "sql"
  };
  return languageMap[ext] || ext || "text";
}

// ============================================================================
// DeepWiki 搜索工具
// ============================================================================

export const deepWikiSearchTool = createTool({
  id: "deepwiki-search",
  description:
    "Use DeepWiki to search for code implementations in a GitHub repository. Perfect for finding real-world examples and best practices. Provide detailed, specific queries for best results.",
  inputSchema: searchCodeSchema,
  outputSchema: searchCodeOutputSchema,
  execute: async ({ context: { query, repository, queryType } }) => {
    const client = new DeepWikiClient();
    const parser = new DeepWikiParser();

    try {
      console.log("Starting DeepWiki search", {
        query: query.substring(0, 50) + "...",
        repository,
        queryType
      });

      // 创建查询
      const queryId = await client.createQuery(query, repository, queryType);
      console.log("Created query", { queryId });

      // 轮询结果
      const response = await client.pollQuery(queryId);
      console.log("Got response", { queriesCount: response.queries?.length || 0 });

      // 解析结果
      const parsed = parser.parseResponse(response);

      // 格式化为 Markdown
      const markdown = parser.formatAsMarkdown(parsed, parsed.codeSnippets);

      const result = {
        success: true,
        repository,
        query,
        queryType,
        summary: parser.generateSummary(parsed),
        content: markdown,
        combinedContent: parsed.combinedContent,
        stats: {
          chunks: parsed.chunks.length,
          references: parsed.references.length,
          fileContents: parsed.fileContents.length,
          codeSnippets: parsed.codeSnippets.length
        },
        codeSnippets: parsed.codeSnippets.map(snippet => ({
          repo: snippet.repo,
          filePath: snippet.filePath,
          content: snippet.content,
          startLine: snippet.startLine,
          endLine: snippet.endLine,
          language: getLanguageFromPath(snippet.filePath)
        }))
      };

      console.log("DeepWiki search completed", {
        repository,
        queryType,
        stats: result.stats
      });

      return result;
    } catch (error) {
      console.error("DeepWiki search failed", { error, query, repository });
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        repository,
        query,
        queryType,
        summary: "Search failed",
        content: "",
        combinedContent: "",
        stats: {
          chunks: 0,
          references: 0,
          fileContents: 0,
          codeSnippets: 0
        },
        codeSnippets: []
      };
    }
  }
});
