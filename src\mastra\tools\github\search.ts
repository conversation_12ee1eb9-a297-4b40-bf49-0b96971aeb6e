import { request } from "undici";

/**
 * GitHub Repository Search Module
 * 基于 GitHub 高级搜索规则构建的仓库搜索工具
 */
export class GitHubSearch {
  constructor(tokenCallback) {
    this.tokenCallback = tokenCallback;
    this.baseUrl = "https://api.github.com";
  }

  /**
   * 构建搜索查询字符串
   * @param {Object} params - 搜索参数
   * @returns {string} 查询字符串
   */
  buildQuery(params) {
    const queryParts = [];

    // 基本搜索词
    if (params.query) {
      queryParts.push(params.query);
    }

    // 用户/组织/仓库限定
    if (params.user) queryParts.push(`user:${params.user}`);
    if (params.org) queryParts.push(`org:${params.org}`);
    if (params.repo) queryParts.push(`repo:${params.repo}`);

    // 搜索范围限定
    if (params.in) {
      const inValues = Array.isArray(params.in) ? params.in : [params.in];
      inValues.forEach(value => queryParts.push(`in:${value}`));
    }

    // 编程语言
    if (params.language) queryParts.push(`language:${params.language}`);

    // 数值范围搜索
    if (params.stars) queryParts.push(`stars:${params.stars}`);
    if (params.forks) queryParts.push(`forks:${params.forks}`);
    if (params.size) queryParts.push(`size:${params.size}`);

    // 日期范围
    if (params.created) queryParts.push(`created:${params.created}`);
    if (params.pushed) queryParts.push(`pushed:${params.pushed}`);

    // 主题
    if (params.topics) {
      const topics = Array.isArray(params.topics) ? params.topics : [params.topics];
      topics.forEach(topic => queryParts.push(`topic:${topic}`));
    }

    // 许可证
    if (params.license) queryParts.push(`license:${params.license}`);

    // 仓库状态
    if (params.is) {
      const isValues = Array.isArray(params.is) ? params.is : [params.is];
      isValues.forEach(value => queryParts.push(`is:${value}`));
    }

    // 归档状态
    if (params.archived !== undefined) {
      queryParts.push(`archived:${params.archived}`);
    }

    return queryParts.join(" ");
  }

  /**
   * 执行仓库搜索
   * @param {Object} searchParams - 搜索参数
   * @returns {Promise<Object>} 搜索结果
   */
  async search(searchParams = {}) {
    try {
      // 获取 GitHub token
      const token = await this.getToken();

      // 构建查询字符串
      const query = this.buildQuery(searchParams);

      if (!query.trim()) {
        throw new Error("搜索查询不能为空");
      }

      // 构建 URL 参数
      const urlParams = new URLSearchParams({
        q: query,
        sort: searchParams.sort || "best-match",
        order: searchParams.order || "desc",
        per_page: searchParams.per_page || 30,
        page: searchParams.page || 1
      });

      const url = `${this.baseUrl}/search/repositories?${urlParams}`;

      // 设置请求头
      const headers = {
        Accept: "application/vnd.github.v3+json",
        "User-Agent": "GitHub-Search-Module"
      };

      if (token) {
        headers["Authorization"] = `token ${token}`;
      }

      // 发送请求
      const { statusCode, body } = await request(url, {
        method: "GET",
        headers
      });

      if (statusCode !== 200) {
        throw new Error(`GitHub API 请求失败: ${statusCode}`);
      }

      const data = await body.json();
      return this.formatResponse(data);
    } catch (error) {
      throw new Error(`搜索失败: ${error.message}`);
    }
  }

  /**
   * 获取 GitHub token
   * @returns {Promise<string|null>} GitHub token
   */
  async getToken() {
    if (!this.tokenCallback) {
      return null;
    }

    try {
      const token = await this.tokenCallback();
      return token;
    } catch (error) {
      console.warn("获取 GitHub token 失败:", error.message);
      return null;
    }
  }

  /**
   * 格式化响应数据
   * @param {Object} data - 原始响应数据
   * @returns {Object} 格式化后的数据
   */
  formatResponse(data) {
    return {
      total_count: data.total_count,
      incomplete_results: data.incomplete_results,
      items: data.items.map(repo => ({
        id: repo.id,
        name: repo.name,
        full_name: repo.full_name,
        description: repo.description,
        html_url: repo.html_url,
        clone_url: repo.clone_url,
        language: repo.language,
        stargazers_count: repo.stargazers_count,
        forks_count: repo.forks_count,
        size: repo.size,
        created_at: repo.created_at,
        updated_at: repo.updated_at,
        pushed_at: repo.pushed_at,
        topics: repo.topics,
        license: repo.license ? repo.license.name : null,
        owner: {
          login: repo.owner.login,
          type: repo.owner.type,
          avatar_url: repo.owner.avatar_url
        }
      }))
    };
  }
}

/**
 * 创建搜索实例的便捷函数
 * @param {Function} tokenCallback - 获取 token 的回调函数
 * @returns {GitHubSearch} 搜索实例
 */
export function createGitHubSearch(tokenCallback) {
  return new GitHubSearch(tokenCallback);
}

// 导出常用的搜索参数示例
export const SearchExamples = {
  // 搜索 React 相关的热门仓库
  reactRepos: {
    query: "react",
    language: "javascript",
    stars: ">1000",
    sort: "stars",
    order: "desc"
  },

  // 搜索特定用户的仓库
  userRepos: username => ({
    user: username,
    sort: "updated",
    order: "desc"
  }),

  // 搜索最近更新的 Python 项目
  recentPython: {
    language: "python",
    pushed: ">2024-01-01",
    sort: "updated",
    order: "desc"
  },

  // 搜索包含特定主题的仓库
  topicSearch: topic => ({
    topics: topic,
    stars: ">100",
    sort: "stars",
    order: "desc"
  })
};
