import { createTool } from "@mastra/core/tools";
import { z } from "zod";
import { search, getNodesDescription } from "./search";
import { scrapeArticle, resolveThreads, getPopularThreads, POPULAR_FORUMS } from "./scraper";
import FireCrawlApp from "@mendable/firecrawl-js";
import * as cheerio from "cheerio";

const app = new FireCrawlApp({ apiKey: process.env.FIRECRAWL_API_KEY || "fc-dd20fa1c0ec4435a871e535d538b4b2f" });

// 搜索结果项接口
interface SearchResultItem {
  title: string;
  url: string;
  author: string;
  replies: number;
  views: number;
  lastPost: string;
  forum: string;
  snippet?: string;
}

// 搜索结果接口
interface SearchResults {
  searchUrl: string;
  results: SearchResultItem[];
  totalResults: number;
  currentPage: number;
  hasNextPage: boolean;
}

/**
 * 解析搜索结果页面
 */
const parseSearchResults = async (searchUrl: string): Promise<SearchResults> => {
  try {
    const scrapeResult = await app.scrapeUrl(searchUrl, {
      formats: ["rawHtml"],
      onlyMainContent: false,
      parsePDF: false,
      maxAge: 14400000
    });

    if (!scrapeResult.success || !scrapeResult.rawHtml) {
      throw new Error("Failed to scrape search results");
    }

    const $ = cheerio.load(scrapeResult.rawHtml);
    const results: SearchResultItem[] = [];

    // 解析搜索结果项
    $(".structItemContainer-group .structItem").each((_, element) => {
      const $item = $(element);

      // 跳过置顶帖子
      if ($item.find(".sticky-thread--hightlighted").length > 0) {
        return;
      }

      const titleElement = $item.find("a[data-preview-url]").first();
      const title = titleElement.text().trim();
      const url = titleElement.attr("href");

      if (!title || !url) return;

      const author = $item.find(".username").first().text().trim();
      const repliesText = $item.find(".structItem-cell--meta dl:first-child dd").text().trim();
      const viewsText = $item.find(".structItem-cell--meta dl:nth-child(2) dd").text().trim();
      const lastPostText =
        $item.find(".structItem-cell--latest time").attr("title") || $item.find(".structItem-cell--latest time").text().trim();
      const forum = $item.find(".structItem-cell--main .structItem-minor").text().trim();
      const snippet = $item.find(".structItem-cell--main .structItem-snippet").text().trim();

      results.push({
        title,
        url: url.startsWith("http") ? url : `https://www.blackhatworld.com${url}`,
        author,
        replies: parseInt(repliesText.replace(/,/g, "")) || 0,
        views: parseInt(viewsText.replace(/,/g, "")) || 0,
        lastPost: lastPostText,
        forum,
        snippet
      });
    });

    // 解析分页信息
    const totalResultsText = $(".block-header").text();
    const totalResults = parseInt(totalResultsText.match(/(\d+)/)?.[1] || "0");
    const currentPage = parseInt($(".pageNav-page--current").text()) || 1;
    const hasNextPage = $(".pageNav-jump--next").length > 0;

    return {
      searchUrl,
      results,
      totalResults,
      currentPage,
      hasNextPage
    };
  } catch (error) {
    console.error("Error parsing search results:", error);
    // 出错时返回空结果
    return {
      searchUrl,
      results: [],
      totalResults: 0,
      currentPage: 1,
      hasNextPage: false
    };
  }
};

const threads = getNodesDescription();

/**
 * BlackHatWorld 搜索工具
 */
export const blackhatWorldSearchTool = createTool({
  id: "blackhatworld-search",
  description: "Search BlackHatWorld forum for posts and discussions",
  inputSchema: z.object({
    keywords: z.string().describe("Search keywords"),
    nodeNames: z
      .array(z.string())
      .optional()
      .describe(`Forum node names to search in (e.g., ["Making Money", "Black Hat SEO"])\n\n${threads}`),
    newerThan: z.string().optional().describe("Start date in YYYY-MM-DD format"),
    olderThan: z.string().optional().describe("End date in YYYY-MM-DD format"),
    order: z.enum(["relevance", "date", "replies"]).optional().default("relevance").describe("Sort order")
  }),
  outputSchema: z.object({
    searchUrl: z.string(),
    results: z
      .array(
        z.object({
          title: z.string(),
          url: z.string(),
          author: z.string(),
          replies: z.number(),
          views: z.number(),
          lastPost: z.string(),
          forum: z.string(),
          snippet: z.string().optional()
        })
      )
      .optional(),
    totalResults: z.number().optional(),
    currentPage: z.number().optional(),
    hasNextPage: z.boolean().optional(),
    availableNodes: z.string().optional()
  }),
  execute: async ({ context }) => {
    const { keywords, nodeNames = [], newerThan = "", olderThan = "", order = "relevance" } = context;

    try {
      // 执行搜索获取重定向URL
      const searchUrl = await search(keywords, nodeNames, newerThan, olderThan, order);

      // 解析搜索结果
      const searchResults = await parseSearchResults(searchUrl);

      return {
        searchUrl: searchResults.searchUrl,
        results: searchResults.results,
        totalResults: searchResults.totalResults,
        currentPage: searchResults.currentPage,
        hasNextPage: searchResults.hasNextPage
      };
    } catch (error) {
      console.error("BlackHatWorld search error:", error);
      throw new Error(`Search failed: ${error.message}`);
    }
  }
});

/**
 * BlackHatWorld 文章抓取工具
 */
export const blackhatWorldScrapeTool = createTool({
  id: "blackhatworld-scrape",
  description: "Scrape article content from BlackHatWorld forum posts",
  inputSchema: z.object({
    url: z.string().describe("URL of the forum post to scrape")
  }),
  outputSchema: z.object({
    url: z.string(),
    title: z.string(),
    content: z.string(),
    author: z.string(),
    postDate: z.string(),
    replies: z.number(),
    views: z.number(),
    success: z.boolean()
  }),
  execute: async ({ context }) => {
    const { url } = context;

    try {
      const article = await scrapeArticle(url);

      if (!article) {
        return {
          url,
          title: "",
          content: "",
          author: "",
          postDate: "",
          replies: 0,
          views: 0,
          success: false
        };
      }

      return {
        ...article,
        success: true
      };
    } catch (error) {
      console.error("Article scraping error:", error);
      return {
        url,
        title: "",
        content: "",
        author: "",
        postDate: "",
        replies: 0,
        views: 0,
        success: false
      };
    }
  }
});

/**
 * BlackHatWorld 热门线程发现工具
 */
export const blackhatWorldDiscoverTool = createTool({
  id: "blackhatworld-discover",
  description: "Discover popular threads from BlackHatWorld forums",
  inputSchema: z.object({
    forumUrl: z.string().optional().describe("Specific forum URL to scrape (optional)"),
    minReplies: z.number().optional().default(10).describe("Minimum number of replies required"),
    usePopularForums: z.boolean().optional().default(true).describe("Use predefined popular forums")
  }),
  outputSchema: z.object({
    threads: z.array(
      z.object({
        url: z.string(),
        title: z.string(),
        replies: z.number(),
        views: z.number(),
        author: z.string(),
        lastPost: z.string()
      })
    ),
    totalFound: z.number(),
    forumsScraped: z.array(z.string())
  }),
  execute: async ({ context }) => {
    const { forumUrl, minReplies = 10, usePopularForums = true } = context;

    try {
      let threads: Awaited<ReturnType<typeof resolveThreads>>;
      let forumsScraped: string[] = [];

      if (forumUrl) {
        // 抓取指定论坛
        threads = await resolveThreads(forumUrl, minReplies);
        forumsScraped = [forumUrl];
      } else if (usePopularForums) {
        // 使用预定义的热门论坛
        threads = await getPopularThreads(minReplies);
        forumsScraped = POPULAR_FORUMS;
      } else {
        threads = [];
      }

      return {
        threads,
        totalFound: threads.length,
        forumsScraped
      };
    } catch (error) {
      console.error("Thread discovery error:", error);
      return {
        threads: [],
        totalFound: 0,
        forumsScraped: []
      };
    }
  }
});
