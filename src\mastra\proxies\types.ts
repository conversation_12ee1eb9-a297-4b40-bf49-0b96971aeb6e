export interface ProxyInfo {
  url: string;
  usageCount: number;
  errorCount: number;
  lastUsed: Date;
  isHealthy: boolean;
}

export interface ProxyPoolConfig {
  refreshIntervalMs: number;
  maxErrorCount: number;
  healthCheckTimeoutMs: number;
}

export interface ProxyStats {
  total: number;
  healthy: number;
  unhealthy: number;
  totalUsage: number;
  totalErrors: number;
  proxies: Array<{
    url: string;
    usageCount: number;
    errorCount: number;
    lastUsed: Date;
    isHealthy: boolean;
  }>;
}
