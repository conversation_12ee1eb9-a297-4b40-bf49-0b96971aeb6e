import { fetchProxies as fetchWebshareProxies } from './webshare';
import type { ProxyInfo, ProxyPoolConfig, ProxyStats } from './types';

export class ProxyPool {
  private proxies: ProxyInfo[] = [];
  private currentIndex = 0;
  private refreshTimer?: NodeJS.Timeout;
  private config: ProxyPoolConfig;

  constructor(config: Partial<ProxyPoolConfig> = {}) {
    this.config = {
      refreshIntervalMs: 30 * 60 * 1000, // 30 minutes
      maxErrorCount: 5,
      healthCheckTimeoutMs: 5000,
      ...config
    };
  }

  async initialize(): Promise<void> {
    await this.refreshProxies();
    this.startRefreshTimer();
  }

  private async refreshProxies(): Promise<void> {
    try {
      const proxyUrls = ['http://127.0.0.1:7890'] // await fetchWebshareProxies();
      const newProxies: ProxyInfo[] = proxyUrls
        .filter(url => url && url.trim())
        .map(url => ({
          url: url.trim(),
          usageCount: 0,
          errorCount: 0,
          lastUsed: new Date(0),
          isHealthy: true
        }));
      
      this.proxies = newProxies;
      console.log(`Refreshed proxy pool with ${this.proxies.length} proxies`);
    } catch (error) {
      console.error('Failed to refresh proxies:', error);
    }
  }

  private startRefreshTimer(): void {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
    }
    this.refreshTimer = setInterval(() => {
      this.refreshProxies();
    }, this.config.refreshIntervalMs);
  }

  getNextProxy(): string | null {
    const healthyProxies = this.proxies.filter(p => 
      p.isHealthy && p.errorCount < this.config.maxErrorCount
    );

    if (healthyProxies.length === 0) {
      return null;
    }

    // Round-robin selection
    const proxy = healthyProxies[this.currentIndex % healthyProxies.length];
    this.currentIndex = (this.currentIndex + 1) % healthyProxies.length;

    // Update usage stats
    proxy.usageCount++;
    proxy.lastUsed = new Date();

    return proxy.url;
  }

  recordError(proxyUrl: string): void {
    const proxy = this.proxies.find(p => p.url === proxyUrl);
    if (proxy) {
      proxy.errorCount++;
      if (proxy.errorCount >= this.config.maxErrorCount) {
        proxy.isHealthy = false;
        console.warn(`Proxy marked as unhealthy: ${proxyUrl} (${proxy.errorCount} errors)`);
      }
    }
  }

  recordSuccess(proxyUrl: string): void {
    const proxy = this.proxies.find(p => p.url === proxyUrl);
    if (proxy) {
      // Reset error count on successful use
      proxy.errorCount = Math.max(0, proxy.errorCount - 1);
    }
  }

  async healthCheck(): Promise<void> {
    const testUrl = 'https://httpbin.org/ip';
    const promises = this.proxies.map(async (proxy) => {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.config.healthCheckTimeoutMs);
        
        const response = await fetch(testUrl, {
          signal: controller.signal,
          // Note: Node.js fetch doesn't support proxy directly, this is conceptual
          // In real implementation, you'd use a library like node-fetch with proxy support
        });
        
        clearTimeout(timeoutId);
        
        if (response.ok) {
          proxy.isHealthy = true;
          proxy.errorCount = Math.max(0, proxy.errorCount - 1);
        } else {
          proxy.errorCount++;
        }
      } catch (error) {
        proxy.errorCount++;
        if (proxy.errorCount >= this.config.maxErrorCount) {
          proxy.isHealthy = false;
        }
      }
    });

    await Promise.allSettled(promises);
    console.log(`Health check completed. Healthy proxies: ${this.proxies.filter(p => p.isHealthy).length}/${this.proxies.length}`);
  }

  getStats(): ProxyStats {
    const healthy = this.proxies.filter(p => p.isHealthy).length;
    const total = this.proxies.length;
    const totalUsage = this.proxies.reduce((sum, p) => sum + p.usageCount, 0);
    const totalErrors = this.proxies.reduce((sum, p) => sum + p.errorCount, 0);

    return {
      total,
      healthy,
      unhealthy: total - healthy,
      totalUsage,
      totalErrors,
      proxies: this.proxies.map(p => ({
        url: p.url.replace(/\/\/.*@/, '//***@'), // Hide credentials in stats
        usageCount: p.usageCount,
        errorCount: p.errorCount,
        lastUsed: p.lastUsed,
        isHealthy: p.isHealthy
      }))
    };
  }

  destroy(): void {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
    }
  }
}
