import { describe, it, expect } from "vitest";
import { 
  deepWikiSearchTool, 
  deepWikiBatchSearchTool, 
  deepWikiLegacyTool 
} from "./improved-index";

describe("DeepWiki Performance Comparison", () => {
  
  describe("Single Search Performance", () => {
    it("should compare simplified vs legacy tool performance", async () => {
      const query = "React hooks implementation patterns";
      const repository = "facebook/react";

      console.log("🔄 Testing simplified tool...");
      const startSimplified = Date.now();
      const simplifiedResult = await deepWikiSearchTool.execute({
        context: { query, repository, focus: "code" }
      });
      const simplifiedTime = Date.now() - startSimplified;

      console.log("🔄 Testing legacy tool...");
      const startLegacy = Date.now();
      const legacyResult = await deepWikiLegacyTool.execute({
        context: { query, repository, queryType: "implementation" }
      });
      const legacyTime = Date.now() - startLegacy;

      // 验证结果质量相似
      expect(simplifiedResult.success).toBe(true);
      expect(legacyResult.success).toBe(true);
      expect(simplifiedResult.codeSnippets.length).toBeGreaterThan(0);
      expect(legacyResult.codeSnippets.length).toBeGreaterThan(0);

      console.log(`📊 Performance Comparison:
        - Simplified Tool: ${simplifiedTime}ms
        - Legacy Tool: ${legacyTime}ms
        - Difference: ${Math.abs(simplifiedTime - legacyTime)}ms
        - Simplified Snippets: ${simplifiedResult.codeSnippets.length}
        - Legacy Snippets: ${legacyResult.codeSnippets.length}`);

      // 性能应该相似（差异不超过20%）
      const performanceDiff = Math.abs(simplifiedTime - legacyTime) / Math.max(simplifiedTime, legacyTime);
      expect(performanceDiff).toBeLessThan(0.2);
    }, 180000);
  });

  describe("Batch Search Performance", () => {
    it("should demonstrate concurrent vs sequential performance", async () => {
      const queries = [
        { query: "authentication patterns", repository: "facebook/react", focus: "code" as const },
        { query: "server-side rendering", repository: "vercel/next.js", focus: "code" as const },
        { query: "testing utilities", repository: "microsoft/playwright", focus: "docs" as const }
      ];

      console.log("🔄 Testing concurrent batch search...");
      const startConcurrent = Date.now();
      const concurrentResult = await deepWikiBatchSearchTool.execute({
        context: { queries, concurrent: true }
      });
      const concurrentTime = Date.now() - startConcurrent;

      console.log("🔄 Testing sequential batch search...");
      const startSequential = Date.now();
      const sequentialResult = await deepWikiBatchSearchTool.execute({
        context: { queries, concurrent: false }
      });
      const sequentialTime = Date.now() - startSequential;

      // 验证结果质量
      expect(concurrentResult.successCount).toBeGreaterThan(0);
      expect(sequentialResult.successCount).toBeGreaterThan(0);
      expect(concurrentResult.results.length).toBe(queries.length);
      expect(sequentialResult.results.length).toBe(queries.length);

      console.log(`📊 Batch Performance Comparison:
        - Concurrent: ${concurrentTime}ms (${concurrentResult.successCount}/${queries.length} success)
        - Sequential: ${sequentialTime}ms (${sequentialResult.successCount}/${queries.length} success)
        - Speed Improvement: ${((sequentialTime - concurrentTime) / sequentialTime * 100).toFixed(1)}%
        - Concurrent Total Snippets: ${concurrentResult.results.reduce((sum, r) => sum + r.codeSnippets.length, 0)}
        - Sequential Total Snippets: ${sequentialResult.results.reduce((sum, r) => sum + r.codeSnippets.length, 0)}`);

      // 并发应该更快（至少快20%）
      expect(concurrentTime).toBeLessThan(sequentialTime * 0.8);
    }, 300000); // 5分钟超时
  });

  describe("QueryType vs Focus Comparison", () => {
    it("should compare queryType effectiveness", async () => {
      const query = "component lifecycle methods";
      const repository = "facebook/react";

      // 测试所有旧的 queryType
      const queryTypes = ["summary", "implementation", "architecture", "general"] as const;
      const results = [];

      for (const queryType of queryTypes) {
        console.log(`🔄 Testing queryType: ${queryType}`);
        const result = await deepWikiLegacyTool.execute({
          context: { query, repository, queryType }
        });
        results.push({ queryType, result });
      }

      // 分析结果差异
      console.log("📊 QueryType Effectiveness Analysis:");
      results.forEach(({ queryType, result }) => {
        console.log(`  - ${queryType}: ${result.codeSnippets.length} snippets, ${result.stats.chunks} chunks`);
      });

      // 检查是否真的有显著差异
      const snippetCounts = results.map(r => r.result.codeSnippets.length);
      const maxSnippets = Math.max(...snippetCounts);
      const minSnippets = Math.min(...snippetCounts);
      const variance = maxSnippets - minSnippets;

      console.log(`📈 Variance Analysis:
        - Max snippets: ${maxSnippets}
        - Min snippets: ${minSnippets}
        - Variance: ${variance}
        - Relative variance: ${(variance / maxSnippets * 100).toFixed(1)}%`);

      // 如果差异小于30%，说明queryType区分度不高
      const relativeVariance = variance / maxSnippets;
      if (relativeVariance < 0.3) {
        console.log("⚠️  QueryType distinction is low - consider simplification");
      } else {
        console.log("✅ QueryType distinction is significant");
      }

      // 所有查询都应该成功
      results.forEach(({ queryType, result }) => {
        expect(result.success).toBe(true);
      });
    }, 240000); // 4分钟超时
  });

  describe("Resource Usage Analysis", () => {
    it("should analyze API call efficiency", async () => {
      const testCases = [
        { name: "Simple Query", query: "useState hook", repository: "facebook/react" },
        { name: "Complex Query", query: "server-side rendering with data fetching and error handling", repository: "vercel/next.js" },
        { name: "Specific Query", query: "test automation patterns", repository: "microsoft/playwright" }
      ];

      console.log("📊 Resource Usage Analysis:");
      
      for (const testCase of testCases) {
        const result = await deepWikiSearchTool.execute({
          context: { 
            query: testCase.query, 
            repository: testCase.repository,
            focus: "code"
          }
        });

        const efficiency = result.codeSnippets.length / (result.executionTime / 1000); // snippets per second
        
        console.log(`  ${testCase.name}:
          - Execution time: ${result.executionTime}ms
          - Code snippets: ${result.codeSnippets.length}
          - File contents: ${result.stats.fileContents}
          - Efficiency: ${efficiency.toFixed(2)} snippets/second`);

        expect(result.success).toBe(true);
        expect(result.executionTime).toBeLessThan(120000); // 不超过2分钟
      }
    }, 300000);
  });
});
