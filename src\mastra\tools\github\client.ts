/**
 * GitHub API Client
 * Handles repository and code search operations
 */

import { request } from "undici";

export interface GitHubSearchParams {
  query: string;
  searchType: "repositories" | "code";
  user?: string;
  org?: string;
  repo?: string;
  language?: string;
  stars?: string;
  forks?: string;
  size?: string;
  created?: string;
  pushed?: string;
  topics?: string[];
  license?: string;
  is?: string[];
  archived?: boolean;
  sort?: string;
  order?: "asc" | "desc";
  per_page?: number;
  page?: number;
}

export interface GitHubRepository {
  id: number;
  name: string;
  full_name: string;
  description: string | null;
  html_url: string;
  clone_url: string;
  language: string | null;
  stargazers_count: number;
  forks_count: number;
  size: number;
  created_at: string;
  updated_at: string;
  pushed_at: string;
  topics: string[];
  license: string | null;
  owner: {
    login: string;
    type: string;
    avatar_url: string;
  };
}

export interface GitHubCodeResult {
  name: string;
  path: string;
  sha: string;
  url: string;
  git_url: string;
  html_url: string;
  repository: {
    id: number;
    name: string;
    full_name: string;
    html_url: string;
    description: string | null;
    language: string | null;
    stargazers_count: number;
    forks_count: number;
  };
  score: number;
}

export interface GitHubSearchResponse {
  total_count: number;
  incomplete_results: boolean;
  items: GitHubRepository[] | GitHubCodeResult[];
}

export class GitHubClient {
  private baseUrl = "https://api.github.com";
  private tokenCallback?: () => Promise<string | null>;

  constructor(tokenCallback?: () => Promise<string | null>) {
    this.tokenCallback = tokenCallback;
  }

  /**
   * Build search query string for GitHub API
   */
  private buildQuery(params: GitHubSearchParams): string {
    const queryParts: string[] = [];

    // Basic search term
    if (params.query) {
      queryParts.push(params.query);
    }

    // User/org/repo qualifiers
    if (params.user) queryParts.push(`user:${params.user}`);
    if (params.org) queryParts.push(`org:${params.org}`);
    if (params.repo) queryParts.push(`repo:${params.repo}`);

    // Language
    if (params.language) queryParts.push(`language:${params.language}`);

    // Numeric range searches
    if (params.stars) queryParts.push(`stars:${params.stars}`);
    if (params.forks) queryParts.push(`forks:${params.forks}`);
    if (params.size) queryParts.push(`size:${params.size}`);

    // Date ranges
    if (params.created) queryParts.push(`created:${params.created}`);
    if (params.pushed) queryParts.push(`pushed:${params.pushed}`);

    // Topics (only for repository search)
    if (params.topics && params.searchType === "repositories") {
      params.topics.forEach(topic => queryParts.push(`topic:${topic}`));
    }

    // License (only for repository search)
    if (params.license && params.searchType === "repositories") {
      queryParts.push(`license:${params.license}`);
    }

    // Repository status (only for repository search)
    if (params.is && params.searchType === "repositories") {
      params.is.forEach(value => queryParts.push(`is:${value}`));
    }

    // Archived status (only for repository search)
    if (params.archived !== undefined && params.searchType === "repositories") {
      queryParts.push(`archived:${params.archived}`);
    }

    return queryParts.join(" ");
  }

  /**
   * Get GitHub token
   */
  private async getToken(): Promise<string | null> {
    if (!this.tokenCallback) {
      return null;
    }

    try {
      return await this.tokenCallback();
    } catch (error) {
      console.warn("Failed to get GitHub token:", error);
      return null;
    }
  }

  /**
   * Execute search request
   */
  async search(params: GitHubSearchParams): Promise<GitHubSearchResponse> {
    const startTime = Date.now();
    
    try {
      console.log(`🔍 Searching GitHub ${params.searchType} for: ${params.query.substring(0, 50)}...`);

      const token = await this.getToken();
      const query = this.buildQuery(params);

      if (!query.trim()) {
        throw new Error("Search query cannot be empty");
      }

      // Build URL parameters
      const urlParams = new URLSearchParams({
        q: query,
        sort: params.sort || "best-match",
        order: params.order || "desc",
        per_page: String(params.per_page || 30),
        page: String(params.page || 1)
      });

      const endpoint = params.searchType === "repositories" ? "repositories" : "code";
      const url = `${this.baseUrl}/search/${endpoint}?${urlParams}`;

      // Set headers
      const headers: Record<string, string> = {
        Accept: "application/vnd.github.v3+json",
        "User-Agent": "Mastra-GitHub-Search"
      };

      if (token) {
        headers["Authorization"] = `token ${token}`;
      }

      // Send request
      const { statusCode, body } = await request(url, {
        method: "GET",
        headers
      });

      if (statusCode !== 200) {
        throw new Error(`GitHub API request failed: ${statusCode}`);
      }

      const data = await body.json() as GitHubSearchResponse;
      const executionTime = Date.now() - startTime;

      console.log(`✅ GitHub search completed in ${executionTime}ms: ${data.total_count} results`);

      return data;
    } catch (error) {
      const executionTime = Date.now() - startTime;
      console.error(`❌ GitHub search failed after ${executionTime}ms:`, error);
      throw new Error(`GitHub search failed: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
}
