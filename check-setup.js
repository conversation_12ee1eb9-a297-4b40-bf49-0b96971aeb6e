const fs = require('fs');

console.log('🔍 Checking BlackHatWorld Agent Test Setup\n');

const files = [
  'src/mastra/agents/blackhatworld-agent.test.ts',
  'src/mastra/agents/blackhatworld-agent.integration.test.ts',
  'vitest.config.ts',
  'globalSetup.ts',
  'testSetup.ts',
  'TESTING.md'
];

let allGood = true;

files.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MISSING`);
    allGood = false;
  }
});

// Check package.json
try {
  const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  console.log('\n📦 Package.json checks:');
  
  if (pkg.scripts && pkg.scripts.test === 'vitest run') {
    console.log('✅ Test script configured');
  } else {
    console.log('❌ Test script not configured');
    allGood = false;
  }
  
  if (pkg.devDependencies && pkg.devDependencies.vitest) {
    console.log('✅ Vitest dependency found');
  } else {
    console.log('❌ Vitest dependency missing');
    allGood = false;
  }
  
  if (pkg.devDependencies && pkg.devDependencies['@mastra/evals']) {
    console.log('✅ Mastra evals dependency found');
  } else {
    console.log('❌ Mastra evals dependency missing');
    allGood = false;
  }
  
} catch (error) {
  console.log('❌ Error reading package.json:', error.message);
  allGood = false;
}

console.log('\n📊 Summary:');
if (allGood) {
  console.log('🎉 All checks passed! Test environment is ready.');
  console.log('\n🚀 Next steps:');
  console.log('   1. npm install');
  console.log('   2. npm test');
} else {
  console.log('⚠️  Some issues found. Please review above.');
}
