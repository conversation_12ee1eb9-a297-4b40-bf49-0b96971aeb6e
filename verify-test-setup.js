#!/usr/bin/env node

/**
 * 验证 BlackHatWorld Agent 测试环境设置
 * 这个脚本检查所有必要的文件和配置是否正确
 */

import { existsSync, readFileSync } from 'fs';
import { join } from 'path';

console.log('🔍 Verifying BlackHatWorld Agent Test Setup\n');

const checks = [];

// 检查文件是否存在
const checkFile = (filePath, description) => {
  const exists = existsSync(filePath);
  checks.push({
    name: description,
    status: exists ? 'PASS' : 'FAIL',
    details: exists ? `Found: ${filePath}` : `Missing: ${filePath}`
  });
  return exists;
};

// 检查文件内容
const checkFileContent = (filePath, searchText, description) => {
  try {
    if (!existsSync(filePath)) {
      checks.push({
        name: description,
        status: 'FAIL',
        details: `File not found: ${filePath}`
      });
      return false;
    }

    const content = readFileSync(filePath, 'utf8');
    const found = content.includes(searchText);
    checks.push({
      name: description,
      status: found ? 'PASS' : 'FAIL',
      details: found ? `Found "${searchText}" in ${filePath}` : `Missing "${searchText}" in ${filePath}`
    });
    return found;
  } catch (error) {
    checks.push({
      name: description,
      status: 'FAIL',
      details: `Error reading ${filePath}: ${error.message}`
    });
    return false;
  }
};

console.log('📁 Checking test files...');

// 检查主要测试文件
checkFile('src/mastra/agents/blackhatworld-agent.test.ts', 'Main test file');
checkFile('src/mastra/agents/blackhatworld-agent.integration.test.ts', 'Integration test file');
checkFile('src/mastra/agents/blackhatworld-agent.ts', 'Agent source file');

// 检查配置文件
checkFile('vitest.config.ts', 'Vitest configuration');
checkFile('globalSetup.ts', 'Global setup file');
checkFile('testSetup.ts', 'Test setup file');
checkFile('package.json', 'Package configuration');

console.log('\n📦 Checking package.json configuration...');

// 检查 package.json 中的测试脚本
checkFileContent('package.json', '"test": "vitest run"', 'Test script in package.json');
checkFileContent('package.json', '"test:agent"', 'Agent test script in package.json');
checkFileContent('package.json', '"vitest"', 'Vitest dependency in package.json');
checkFileContent('package.json', '"@mastra/evals"', 'Mastra evals dependency in package.json');

console.log('\n🧪 Checking test file content...');

// 检查测试文件内容
checkFileContent(
  'src/mastra/agents/blackhatworld-agent.test.ts',
  'AnswerRelevancyMetric',
  'Answer relevancy metric import'
);
checkFileContent(
  'src/mastra/agents/blackhatworld-agent.test.ts',
  'blackhatWorldAgent',
  'Agent import in test file'
);
checkFileContent(
  'src/mastra/agents/blackhatworld-agent.test.ts',
  'USE_MOCK_DATA',
  'Mock data configuration in test'
);

console.log('\n⚙️ Checking configuration files...');

// 检查配置文件内容
checkFileContent('vitest.config.ts', 'globalSetup', 'Global setup in vitest config');
checkFileContent('vitest.config.ts', 'setupFiles', 'Setup files in vitest config');
checkFileContent('globalSetup.ts', 'globalSetup', 'Global setup function');
checkFileContent('testSetup.ts', 'attachListeners', 'Attach listeners in test setup');

console.log('\n🔧 Checking agent configuration...');

// 检查 agent 文件
checkFileContent(
  'src/mastra/agents/blackhatworld-agent.ts',
  'blackhatWorldSearchTool',
  'Search tool in agent'
);
checkFileContent(
  'src/mastra/agents/blackhatworld-agent.ts',
  'blackhatWorldScrapeTool',
  'Scrape tool in agent'
);

// 检查 mastra 配置
checkFileContent(
  'src/mastra/index.ts',
  'blackhatWorldAgent',
  'Agent in mastra configuration'
);

console.log('\n📊 Test Setup Verification Results');
console.log('=' .repeat(50));

let passCount = 0;
let failCount = 0;

checks.forEach(check => {
  const icon = check.status === 'PASS' ? '✅' : '❌';
  console.log(`${icon} ${check.name}`);
  if (check.status === 'FAIL') {
    console.log(`   ${check.details}`);
    failCount++;
  } else {
    passCount++;
  }
});

console.log('\n📈 Summary:');
console.log(`   ✅ Passed: ${passCount}`);
console.log(`   ❌ Failed: ${failCount}`);
console.log(`   📊 Total: ${checks.length}`);

if (failCount === 0) {
  console.log('\n🎉 All checks passed! Your test environment is ready.');
  console.log('\n🚀 Next steps:');
  console.log('   1. Install dependencies: npm install');
  console.log('   2. Run tests: npm test');
  console.log('   3. Run specific tests: npm run test:agent');
  console.log('   4. Run with coverage: npm run test:coverage');
} else {
  console.log('\n⚠️  Some checks failed. Please review the issues above.');
  console.log('\n🔧 Common fixes:');
  console.log('   1. Make sure all files are created correctly');
  console.log('   2. Check file paths and imports');
  console.log('   3. Verify package.json dependencies');
  console.log('   4. Ensure configuration files are properly set up');
  
  process.exit(1);
}

console.log('\n📚 Documentation:');
console.log('   - Test guide: TESTING.md');
console.log('   - Mastra docs: https://docs.mastra.ai/docs/evals/overview');
console.log('   - Vitest docs: https://vitest.dev/');

console.log('\n✨ Happy testing!');
