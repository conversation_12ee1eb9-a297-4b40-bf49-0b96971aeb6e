import { describe, it, expect, beforeAll } from "vitest";
import { blackhatWorldAgent } from "./blackhatworld-agent";

// 集成测试 - 测试真实的工具集成和性能
describe("BlackHatWorld Agent Integration Tests", () => {


  describe("Tool Integration", () => {
    it("should integrate search tool effectively", async () => {
      const startTime = Date.now();

      const response = await blackhatWorldAgent.generate(
        "Search BlackHatWorld for posts about 'content farm strategies' in the Making Money section, sorted by replies"
      );

      const duration = Date.now() - startTime;

      expect(response).toBeDefined();
      expect(typeof response).toBe("string");
      expect(response.text.length).toBeGreaterThan(50);
      expect(duration).toBeLessThan(30000); // 应该在30秒内完成

      console.log(`Search integration test completed in ${duration}ms`);
      console.log("Response preview:", response.text.substring(0, 200) + "...");
    });

    it("should integrate scrape tool effectively", async () => {
      const startTime = Date.now();

      const response = await blackhatWorldAgent.generate(
        "Please scrape and analyze the content from this BlackHatWorld post: https://www.blackhatworld.com/seo/monetizing-mobile-phone-farms-in-2025.1707940/"
      );

      const duration = Date.now() - startTime;

      expect(response).toBeDefined();
      expect(typeof response).toBe("string");
      expect(response.text.length).toBeGreaterThan(50);
      expect(duration).toBeLessThan(45000); // 抓取可能需要更长时间

      console.log(`Scrape integration test completed in ${duration}ms`);
      console.log("Response preview:", response.text.substring(0, 200) + "...");
    });

    it("should handle complex multi-step requests", async () => {
      const response = await blackhatWorldAgent.generate(`
        I need help with research on mobile phone farming for affiliate marketing:
        1. First search for recent discussions about mobile phone farms
        2. Then find posts specifically about monetization strategies
        3. Provide a summary of the key insights
      `);

      expect(response).toBeDefined();
      expect(response.text.length).toBeGreaterThan(100);

      // 检查是否包含多步骤处理的迹象
      const lowerResponse = response.text.toLowerCase();
      expect(
        lowerResponse.includes("search") ||
          lowerResponse.includes("found") ||
          lowerResponse.includes("summary") ||
          lowerResponse.includes("insights")
      ).toBe(true);
    });
  });

  describe("Response Quality", () => {
    it("should provide structured responses", async () => {
      const response = await blackhatWorldAgent.generate("What are the best practices for searching BlackHatWorld effectively?");

      expect(response).toBeDefined();

      // 检查结构化内容的迹象
      const hasStructure =
        response.text.includes("1.") ||
        response.text.includes("•") ||
        response.text.includes("-") ||
        response.text.includes("**") ||
        response.text.includes("##");

      expect(hasStructure).toBe(true);
    });

    it("should provide actionable advice", async () => {
      const response = await blackhatWorldAgent.generate(
        "I'm new to BlackHatWorld. How should I start researching affiliate marketing strategies?"
      );

      expect(response).toBeDefined();

      // 检查可操作建议的关键词
      const lowerResponse = response.text.toLowerCase();
      const hasActionableContent =
        lowerResponse.includes("start") ||
        lowerResponse.includes("begin") ||
        lowerResponse.includes("first") ||
        lowerResponse.includes("step") ||
        lowerResponse.includes("recommend") ||
        lowerResponse.includes("suggest");

      expect(hasActionableContent).toBe(true);
    });
  });

  describe("Error Handling", () => {
    it("should handle empty queries gracefully", async () => {
      const response = await blackhatWorldAgent.generate("");

      expect(response).toBeDefined();
      expect(response.text.length).toBeGreaterThan(10);
    });

    it("should handle very long queries", async () => {
      const longQuery = "search for " + "affiliate marketing ".repeat(50) + "strategies";

      const response = await blackhatWorldAgent.generate(longQuery);

      expect(response).toBeDefined();
      expect(response.text.length).toBeGreaterThan(20);
    });

    it("should handle queries with special characters", async () => {
      const response = await blackhatWorldAgent.generate(
        "Search for posts about 'affiliate marketing' & 'content farms' (2024) - latest trends!"
      );

      expect(response).toBeDefined();
      expect(response.text.length).toBeGreaterThan(20);
    });
  });

  describe("Memory and Context", () => {
    it("should maintain context across interactions", async () => {
      // 第一个查询
      const response1 = await blackhatWorldAgent.generate("I'm interested in mobile phone farming for affiliate marketing");

      expect(response1).toBeDefined();

      // 第二个查询，应该记住上下文
      const response2 = await blackhatWorldAgent.generate("Can you search for more specific information about this topic?");

      expect(response2).toBeDefined();
      expect(response2.text.length).toBeGreaterThan(20);

      // 检查是否引用了之前的主题
      const lowerResponse2 = response2.text.toLowerCase();
      expect(
        lowerResponse2.includes("mobile") ||
          lowerResponse2.includes("phone") ||
          lowerResponse2.includes("farm") ||
          lowerResponse2.includes("affiliate")
      ).toBe(true);
    });
  });

  describe("Performance Benchmarks", () => {
    it("should respond to simple queries quickly", async () => {
      const startTime = Date.now();

      const response = await blackhatWorldAgent.generate("Hello");

      const duration = Date.now() - startTime;

      expect(response).toBeDefined();
      expect(duration).toBeLessThan(15000); // 简单查询应该在15秒内完成

      console.log(`Simple query benchmark: ${duration}ms`);
    });

    it("should handle concurrent requests", async () => {
      const queries = [
        "What is BlackHatWorld?",
        "How do I search for affiliate marketing?",
        "What are the main forum categories?"
      ];

      const startTime = Date.now();

      const responses = await Promise.all(queries.map(query => blackhatWorldAgent.generate(query)));

      const duration = Date.now() - startTime;

      expect(responses).toHaveLength(3);
      responses.forEach(response => {
        expect(response).toBeDefined();
        expect(response.text.length).toBeGreaterThan(10);
      });

      console.log(`Concurrent requests benchmark: ${duration}ms for ${queries.length} queries`);
    });
  });
});
