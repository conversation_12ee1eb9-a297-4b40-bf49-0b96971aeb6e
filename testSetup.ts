import { beforeAll, afterAll } from "vitest";
import { attachListeners } from "@mastra/evals";
import { mastra } from "./src/mastra/index";

beforeAll(async () => {
  console.log("🧪 Setting up test environment for BlackHatWorld Agent");

  // 附加评估监听器，将结果存储到Mastra存储中
  await attachListeners(mastra);

  console.log("✅ Test setup completed - using mock data mode");
});

afterAll(async () => {
  console.log("🧹 Cleaning up test environment");

  console.log("✅ Test cleanup completed");
});
