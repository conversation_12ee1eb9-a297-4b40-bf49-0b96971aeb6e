import { describe, it, expect, beforeAll } from "vitest";
import { githubAgent } from "./github-agent";

// Setup test environment
beforeAll(() => {
  process.env.NODE_ENV = "test";
});

describe("GitHub Agent Tests", () => {
  
  describe("Basic Functionality", () => {
    it("should respond to basic queries", async () => {
      const response = await githubAgent.generate("Hello, can you help me search GitHub repositories?");
      
      expect(response).toBeDefined();
      expect(response.text).toBeDefined();
      expect(typeof response.text).toBe("string");
      expect(response.text.length).toBeGreaterThan(0);
      console.log("Basic response length:", response.text.length);
    }, 60000);

    it("should mention GitHub search capabilities when asked", async () => {
      const response = await githubAgent.generate(
        "What can you help me with regarding GitHub search?"
      );
      
      expect(response).toBeDefined();
      expect(response.text).toBeDefined();
      expect(response.text.toLowerCase()).toContain("github");
      console.log("GitHub search response contains 'github':", response.text.toLowerCase().includes("github"));
    }, 60000);
  });

  describe("Search Type Understanding", () => {
    it("should understand repository vs code search differences", async () => {
      const response = await githubAgent.generate(
        "What's the difference between repository search and code search?"
      );
      
      expect(response).toBeDefined();
      const lowerResponse = response.text.toLowerCase();
      
      // Should mention both search types
      expect(
        lowerResponse.includes("repository") && 
        lowerResponse.includes("code")
      ).toBe(true);
      
      console.log("Response explains search types:", 
        lowerResponse.includes("repository") && lowerResponse.includes("code"));
    }, 60000);

    it("should explain batch search capabilities", async () => {
      const response = await githubAgent.generate(
        "Can you search multiple GitHub queries at once?"
      );
      
      expect(response).toBeDefined();
      const lowerResponse = response.text.toLowerCase();
      
      expect(
        lowerResponse.includes("batch") || 
        lowerResponse.includes("multiple") || 
        lowerResponse.includes("concurrent") ||
        lowerResponse.includes("parallel")
      ).toBe(true);
      
      console.log("Response mentions batch capabilities:", 
        lowerResponse.includes("batch") || lowerResponse.includes("multiple"));
    }, 60000);
  });

  describe("Domain Knowledge Tests", () => {
    it("should demonstrate knowledge of GitHub search filters", async () => {
      const response = await githubAgent.generate(
        "What search filters are available for GitHub repositories?"
      );
      
      const lowerResponse = response.text.toLowerCase();
      
      // Should mention common filters
      expect(
        lowerResponse.includes("language") || 
        lowerResponse.includes("stars") || 
        lowerResponse.includes("topics") ||
        lowerResponse.includes("user") ||
        lowerResponse.includes("organization")
      ).toBe(true);
      
      console.log("Mentions search filters:", 
        lowerResponse.includes("language") || lowerResponse.includes("stars"));
    }, 60000);

    it("should provide search optimization tips", async () => {
      const response = await githubAgent.generate(
        "How can I optimize my GitHub searches to find better results?"
      );
      
      const lowerResponse = response.text.toLowerCase();
      expect(
        lowerResponse.includes("specific") || 
        lowerResponse.includes("keyword") || 
        lowerResponse.includes("filter") ||
        lowerResponse.includes("stars") ||
        lowerResponse.includes("language")
      ).toBe(true);
    }, 60000);
  });

  describe("Error Handling Tests", () => {
    it("should handle vague queries gracefully", async () => {
      const response = await githubAgent.generate("Help me find code");

      expect(response).toBeDefined();
      expect(response.text.length).toBeGreaterThan(20);
      
      const lowerResponse = response.text.toLowerCase();
      expect(
        lowerResponse.includes("search") || 
        lowerResponse.includes("github") || 
        lowerResponse.includes("repository") ||
        lowerResponse.includes("code")
      ).toBe(true);
      
      console.log("Vague query response length:", response.text.length);
    }, 60000);

    it("should handle specific technology requests", async () => {
      const response = await githubAgent.generate(
        "Find me React libraries for building user interfaces"
      );

      expect(response).toBeDefined();
      expect(response.text.length).toBeGreaterThan(10);
      
      const lowerResponse = response.text.toLowerCase();
      expect(
        lowerResponse.includes("react") || 
        lowerResponse.includes("library") || 
        lowerResponse.includes("ui") ||
        lowerResponse.includes("interface")
      ).toBe(true);
      
      console.log(
        "Technology request response contains relevant terms:",
        lowerResponse.includes("react") || lowerResponse.includes("library")
      );
    }, 60000);
  });

  describe("Tool Integration Tests", () => {
    it("should effectively use GitHub search tool for repositories", async () => {
      const response = await githubAgent.generate(
        "Search for popular JavaScript testing frameworks on GitHub"
      );
      
      expect(response).toBeDefined();
      const lowerResponse = response.text.toLowerCase();
      expect(
        lowerResponse.includes("search") || 
        lowerResponse.includes("javascript") || 
        lowerResponse.includes("testing") ||
        lowerResponse.includes("framework")
      ).toBe(true);
    }, 120000);

    it("should handle code search requests", async () => {
      const response = await githubAgent.generate(
        "Find examples of JWT authentication implementation in Node.js projects"
      );
      
      expect(response).toBeDefined();
      const lowerResponse = response.text.toLowerCase();
      expect(
        lowerResponse.includes("jwt") ||
        lowerResponse.includes("authentication") ||
        lowerResponse.includes("node") ||
        lowerResponse.includes("code")
      ).toBe(true);
    }, 120000);

    it("should handle batch search requests", async () => {
      const response = await githubAgent.generate(
        "I need to compare React, Vue, and Angular frameworks. Can you search for all of them efficiently?"
      );
      
      expect(response).toBeDefined();
      const lowerResponse = response.text.toLowerCase();
      expect(
        lowerResponse.includes("batch") ||
        lowerResponse.includes("multiple") ||
        lowerResponse.includes("concurrent") ||
        lowerResponse.includes("compare") ||
        lowerResponse.includes("efficient")
      ).toBe(true);
    }, 120000);
  });

  describe("Performance Awareness Tests", () => {
    it("should understand GitHub API benefits", async () => {
      const response = await githubAgent.generate(
        "What are the advantages of using GitHub's search API?"
      );
      
      expect(response).toBeDefined();
      const lowerResponse = response.text.toLowerCase();
      
      // Should mention API benefits
      expect(
        lowerResponse.includes("api") ||
        lowerResponse.includes("search") ||
        lowerResponse.includes("filter") ||
        lowerResponse.includes("comprehensive") ||
        lowerResponse.includes("accurate")
      ).toBe(true);
      
      console.log("Mentions API benefits:", 
        lowerResponse.includes("api") || lowerResponse.includes("search"));
    }, 60000);

    it("should recommend appropriate search strategies", async () => {
      const response = await githubAgent.generate(
        "I need to find the best machine learning libraries across different languages. What's the best approach?"
      );
      
      expect(response).toBeDefined();
      const lowerResponse = response.text.toLowerCase();
      
      // Should suggest efficient search strategies
      expect(
        lowerResponse.includes("batch") ||
        lowerResponse.includes("filter") ||
        lowerResponse.includes("language") ||
        lowerResponse.includes("stars") ||
        lowerResponse.includes("multiple")
      ).toBe(true);
      
      console.log("Recommends efficient strategies:", 
        lowerResponse.includes("batch") || lowerResponse.includes("filter"));
    }, 60000);
  });
});
