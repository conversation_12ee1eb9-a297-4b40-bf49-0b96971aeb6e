import { blackhatWorldSearchTool, blackhatWorldScrapeTool, blackhatWorldDiscoverTool } from './index';

/**
 * 示例：如何使用 BlackHatWorld 搜索工具
 */
async function exampleSearch() {
  console.log('=== BlackHatWorld Search Example ===');
  
  try {
    // 基本搜索
    const searchResult = await blackhatWorldSearchTool.execute({
      context: {
        keywords: 'content farm',
        order: 'relevance',
        parseResults: true
      }
    });

    console.log('Search URL:', searchResult.searchUrl);
    console.log('Total Results:', searchResult.totalResults);
    console.log('Results Found:', searchResult.results?.length);
    
    if (searchResult.results && searchResult.results.length > 0) {
      console.log('\nFirst 3 results:');
      searchResult.results.slice(0, 3).forEach((result, index) => {
        console.log(`${index + 1}. ${result.title}`);
        console.log(`   URL: ${result.url}`);
        console.log(`   Author: ${result.author} | Replies: ${result.replies} | Views: ${result.views}`);
        console.log(`   Forum: ${result.forum}`);
        if (result.snippet) {
          console.log(`   Snippet: ${result.snippet.substring(0, 100)}...`);
        }
        console.log('');
      });
    }

  } catch (error) {
    console.error('Search failed:', error);
  }
}

/**
 * 示例：如何使用高级搜索功能
 */
async function exampleAdvancedSearch() {
  console.log('=== Advanced Search Example ===');
  
  try {
    // 在特定论坛节点中搜索
    const advancedResult = await blackhatWorldSearchTool.execute({
      context: {
        keywords: 'affiliate marketing',
        nodeNames: ['Making Money', 'Affiliate Programs'],
        newerThan: '2024-01-01',
        order: 'replies',
        parseResults: true
      }
    });

    console.log('Advanced Search Results:', advancedResult.results?.length);
    console.log('Search URL:', advancedResult.searchUrl);

  } catch (error) {
    console.error('Advanced search failed:', error);
  }
}

/**
 * 示例：如何发现热门线程
 */
async function exampleDiscoverThreads() {
  console.log('=== Thread Discovery Example ===');
  
  try {
    // 发现热门线程
    const discoverResult = await blackhatWorldDiscoverTool.execute({
      context: {
        minReplies: 15,
        usePopularForums: true
      }
    });

    console.log('Threads discovered:', discoverResult.totalFound);
    console.log('Forums scraped:', discoverResult.forumsScraped.length);
    
    if (discoverResult.threads.length > 0) {
      console.log('\nTop 5 threads by replies:');
      const sortedThreads = discoverResult.threads
        .sort((a, b) => b.replies - a.replies)
        .slice(0, 5);
        
      sortedThreads.forEach((thread, index) => {
        console.log(`${index + 1}. ${thread.title}`);
        console.log(`   Replies: ${thread.replies} | Views: ${thread.views}`);
        console.log(`   Author: ${thread.author} | Last Post: ${thread.lastPost}`);
        console.log(`   URL: ${thread.url}`);
        console.log('');
      });
    }

  } catch (error) {
    console.error('Thread discovery failed:', error);
  }
}

/**
 * 示例：如何抓取文章内容
 */
async function exampleScrapeArticle() {
  console.log('=== Article Scraping Example ===');
  
  try {
    // 抓取指定文章
    const articleUrl = 'https://www.blackhatworld.com/seo/amazon-associates-alternative-tier-1-traffic-500k-clicks-month.1734069/';
    
    const scrapeResult = await blackhatWorldScrapeTool.execute({
      context: {
        url: articleUrl
      }
    });

    if (scrapeResult.success) {
      console.log('Article scraped successfully!');
      console.log('Title:', scrapeResult.title);
      console.log('Author:', scrapeResult.author);
      console.log('Content length:', scrapeResult.content.length, 'characters');
      console.log('Content preview:', scrapeResult.content.substring(0, 200) + '...');
    } else {
      console.log('Failed to scrape article');
    }

  } catch (error) {
    console.error('Article scraping failed:', error);
  }
}

/**
 * 完整工作流示例：搜索 -> 发现 -> 抓取
 */
async function exampleCompleteWorkflow() {
  console.log('=== Complete Workflow Example ===');
  
  try {
    // 1. 搜索相关主题
    console.log('Step 1: Searching for relevant topics...');
    const searchResult = await blackhatWorldSearchTool.execute({
      context: {
        keywords: 'passive income',
        nodeNames: ['Making Money'],
        order: 'replies',
        parseResults: true
      }
    });

    if (!searchResult.results || searchResult.results.length === 0) {
      console.log('No search results found');
      return;
    }

    // 2. 选择最有趣的结果（按回复数排序）
    console.log('Step 2: Selecting most engaging posts...');
    const topPosts = searchResult.results
      .filter(result => result.replies > 10)
      .sort((a, b) => b.replies - a.replies)
      .slice(0, 3);

    console.log(`Found ${topPosts.length} high-engagement posts`);

    // 3. 抓取选中的文章内容
    console.log('Step 3: Scraping selected articles...');
    for (const post of topPosts) {
      console.log(`\nScraping: ${post.title}`);
      
      const articleContent = await blackhatWorldScrapeTool.execute({
        context: {
          url: post.url
        }
      });

      if (articleContent.success) {
        console.log(`✓ Successfully scraped ${articleContent.content.length} characters`);
        // 这里可以进一步处理内容，比如保存到数据库或进行分析
      } else {
        console.log('✗ Failed to scrape this article');
      }
    }

  } catch (error) {
    console.error('Workflow failed:', error);
  }
}

// 运行示例（取消注释来测试）
async function runExamples() {
  console.log('BlackHatWorld Tools Examples\n');
  
  // 设置开发模式使用模拟数据
  process.env.USE_MOCK_DATA = 'true';
  
  await exampleSearch();
  console.log('\n' + '='.repeat(50) + '\n');
  
  await exampleAdvancedSearch();
  console.log('\n' + '='.repeat(50) + '\n');
  
  // 注意：以下示例需要实际网络请求，在开发时可能需要注释掉
  // await exampleDiscoverThreads();
  // console.log('\n' + '='.repeat(50) + '\n');
  
  // await exampleScrapeArticle();
  // console.log('\n' + '='.repeat(50) + '\n');
  
  // await exampleCompleteWorkflow();
}

// 导出示例函数供外部使用
export {
  exampleSearch,
  exampleAdvancedSearch,
  exampleDiscoverThreads,
  exampleScrapeArticle,
  exampleCompleteWorkflow,
  runExamples
};

// 如果直接运行此文件，执行示例
if (require.main === module) {
  runExamples().catch(console.error);
}
